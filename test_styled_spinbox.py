#!/usr/bin/env python3
"""
Test script to identify spinbox styling issues with theme applied
"""
import sys
import os
sys.path.insert(0, '/mnt/HDD/ak_devel/AT3Gui/src')

from PyQt6.QtWidgets import <PERSON>App<PERSON>, QWidget, QVBoxLayout, QSpinBox, QDoubleSpinBox, QLabel, QGroupBox
from PyQt6.QtCore import Qt
from gui.theme_manager import ThemeManager

class StyledSpinBoxTest(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        
        # Test without styling
        group1 = QGroupBox("Default Styling")
        group1_layout = QVBoxLayout()
        
        group1_layout.addWidget(QLabel("QSpinBox (default):"))
        spinbox1 = QSpinBox()
        spinbox1.setRange(0, 100)
        spinbox1.setValue(50)
        group1_layout.addWidget(spinbox1)
        
        group1_layout.addWidget(QLabel("QDoubleSpinBox (default):"))
        double_spinbox1 = QDoubleSpinBox()
        double_spinbox1.setRange(0.0, 100.0)
        double_spinbox1.setValue(50.0)
        group1_layout.addWidget(double_spinbox1)
        
        group1.setLayout(group1_layout)
        layout.addWidget(group1)
        
        # Test with fixed width (like in main app)
        group2 = QGroupBox("Fixed Width (like main app)")
        group2_layout = QVBoxLayout()
        
        group2_layout.addWidget(QLabel("Fixed Width QSpinBox:"))
        spinbox2 = QSpinBox()
        spinbox2.setRange(0, 100)
        spinbox2.setValue(50)
        spinbox2.setFixedWidth(80)
        group2_layout.addWidget(spinbox2)
        
        group2_layout.addWidget(QLabel("Fixed Width QDoubleSpinBox:"))
        double_spinbox2 = QDoubleSpinBox()
        double_spinbox2.setRange(0.0, 100.0)
        double_spinbox2.setValue(50.0)
        double_spinbox2.setFixedWidth(80)
        group2_layout.addWidget(double_spinbox2)
        
        group2.setLayout(group2_layout)
        layout.addWidget(group2)
        
        self.setLayout(layout)
        self.setWindowTitle('Styled SpinBox Arrow Test')
        self.resize(400, 300)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # Apply the same theme as the main app
    theme_manager = ThemeManager()
    app.setStyleSheet(theme_manager.get_theme_stylesheet())
    
    window = StyledSpinBoxTest()
    window.show()
    
    print("SpinBox styling test launched with theme applied")
    print("Check if up/down arrows are visible on the spinboxes")
    
    sys.exit(app.exec())
