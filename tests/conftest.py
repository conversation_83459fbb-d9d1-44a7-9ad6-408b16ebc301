"""
PyTest configuration and fixtures for AreTomo3 GUI tests.
"""
import pytest
from PyQt6.QtWidgets import QApplication
import sys
import os
from pathlib import Path

@pytest.fixture(scope="session")
def qt_application():
    """Create a Qt Application for the test session."""
    app = QApplication(sys.argv)
    yield app
    app.quit()

@pytest.fixture
def temp_dir(tmp_path):
    """Create a temporary directory for test files."""
    return tmp_path

@pytest.fixture
def resource_monitor():
    """Create and configure a resource monitor for testing."""
    from aretomo3_gui.core.resource_manager import get_resource_monitor
    monitor = get_resource_monitor()
    monitor.start_monitoring()
    yield monitor
    monitor.stop_monitoring()

@pytest.fixture
def thread_manager():
    """Create and configure a thread manager for testing."""
    from aretomo3_gui.core.thread_manager import get_thread_manager
    manager = get_thread_manager()
    yield manager
    manager.shutdown(wait=True)

@pytest.fixture
def aretomo_env():
    """Set up AreTomo3 environment for testing."""
    old_path = os.environ.get('ARETOMO3_PATH', '')
    os.environ['ARETOMO3_PATH'] = '/usr/local/bin/AreTomo3'
    yield
    if old_path:
        os.environ['ARETOMO3_PATH'] = old_path
    else:
        del os.environ['ARETOMO3_PATH']

@pytest.fixture
def test_series_dir(tmp_path):
    """Create a test directory with tilt series data."""
    series_dir = tmp_path / "test_series"
    series_dir.mkdir()

    # Create test files
    for i in range(2):
        series_name = f"test_{i:03d}"
        (series_dir / series_name).mkdir()
        for j in range(3):
            (series_dir / series_name / f"{series_name}_{j:02d}.eer").touch()
        (series_dir / series_name / f"{series_name}.mdoc").write_text("Test MDOC content")

    return series_dir

@pytest.fixture
def real_test_data():
    """Fixture providing path to real test data in Test_Input_1."""
    return Path(__file__).parent / "Test_Input_1"
