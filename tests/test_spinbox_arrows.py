#!/usr/bin/env python3
"""Test script to verify spinbox arrow visibility fix."""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QSpinBox, QDoubleSpinBox, QLabel, QPushButton)
from PyQt6.QtCore import Qt

# Add the src directory to path so we can import the theme manager
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from gui.theme_manager import ThemeManager
except ImportError:
    # Try alternative path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aretomo3_gui'))
    from gui.theme_manager import ThemeManager

class SpinBoxTestWindow(QMainWindow):
    """Test window to verify spinbox arrow visibility."""
    
    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager()
        self.init_ui()
        self.apply_theme()
        
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("SpinBox Arrow Visibility Test")
        self.setGeometry(200, 200, 500, 400)
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Add title
        title = QLabel("SpinBox Arrow Visibility Test")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Add instruction
        instruction = QLabel("The spinbox buttons should now show proper arrow symbols instead of black rectangles.")
        instruction.setAlignment(Qt.AlignmentFlag.AlignCenter)
        instruction.setWordWrap(True)
        layout.addWidget(instruction)
        
        # Create test spinboxes
        spinbox_layout = QVBoxLayout()
        
        # Integer spinbox
        int_layout = QHBoxLayout()
        int_layout.addWidget(QLabel("Integer SpinBox:"))
        int_spinbox = QSpinBox()
        int_spinbox.setRange(-100, 100)
        int_spinbox.setValue(0)
        int_layout.addWidget(int_spinbox)
        spinbox_layout.addLayout(int_layout)
        
        # Double spinbox
        double_layout = QHBoxLayout()
        double_layout.addWidget(QLabel("Double SpinBox:"))
        double_spinbox = QDoubleSpinBox()
        double_spinbox.setRange(-100.0, 100.0)
        double_spinbox.setValue(0.0)
        double_spinbox.setDecimals(2)
        double_layout.addWidget(double_spinbox)
        spinbox_layout.addLayout(double_layout)
        
        # Large spinbox for better visibility
        large_layout = QHBoxLayout()
        large_layout.addWidget(QLabel("Large SpinBox:"))
        large_spinbox = QSpinBox()
        large_spinbox.setRange(0, 1000)
        large_spinbox.setValue(50)
        large_spinbox.setStyleSheet("font-size: 14px; min-height: 30px;")
        large_layout.addWidget(large_spinbox)
        spinbox_layout.addLayout(large_layout)
        
        layout.addLayout(spinbox_layout)
        
        # Add theme toggle buttons
        theme_layout = QHBoxLayout()
        
        dark_button = QPushButton("Dark Theme")
        dark_button.clicked.connect(lambda: self.switch_theme("dark"))
        theme_layout.addWidget(dark_button)
        
        light_button = QPushButton("Light Theme")
        light_button.clicked.connect(lambda: self.switch_theme("light"))
        theme_layout.addWidget(light_button)
        
        layout.addLayout(theme_layout)
        
        # Add test results info
        results_label = QLabel()
        results_label.setText(
            "Expected Results:\n"
            "✓ Spinbox up/down buttons should show triangular arrows\n"
            "✓ Arrows should be white in dark theme, dark in light theme\n"
            "✓ Buttons should respond to hover/click with color changes\n"
            "✓ No black rectangles or squares should be visible"
        )
        results_label.setStyleSheet("background-color: rgba(0,100,200,0.1); padding: 10px; border-radius: 5px;")
        layout.addWidget(results_label)
        
        layout.addStretch()
        
    def switch_theme(self, theme_name):
        """Switch to the specified theme."""
        self.theme_manager.set_theme(theme_name)
        self.apply_theme()
        
    def apply_theme(self):
        """Apply the current theme to the application."""
        stylesheet = self.theme_manager.get_theme_stylesheet()
        self.setStyleSheet(stylesheet)

def main():
    """Run the test application."""
    app = QApplication(sys.argv)
    
    # Create and show the test window
    window = SpinBoxTestWindow()
    window.show()
    
    print("SpinBox Arrow Test Started")
    print("=" * 50)
    print("Testing spinbox arrow visibility fix...")
    print("Look for the following:")
    print("- Spinbox buttons should show triangular arrows (not black rectangles)")
    print("- Arrows should be visible in both light and dark themes")
    print("- Hover effects should work on buttons")
    print("=" * 50)
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
