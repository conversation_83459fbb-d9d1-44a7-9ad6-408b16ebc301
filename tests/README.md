# 🧪 AT3GUI Test Suite

## 📁 **Test Directory Structure**

```
tests/
├── README.md                          # This documentation
├── conftest.py                        # Pytest configuration
├── __init__.py                        # Package initialization
│
├── 🔍 validation/                     # Import & Basic Validation Tests
│   └── test_comprehensive_imports.py  # Import validation (88.9% success)
│
├── 🔧 unit/                           # Unit Tests
│   ├── test_core_functionality.py     # Core system tests
│   └── test_gui_basic.py              # GUI component tests
│
├── 🔗 integration/                    # Integration Tests
│   ├── test_application_startup.py    # Application startup tests
│   └── test_mrc_viewer.py             # MRC file handling tests
│
├── 📊 Test_Input_1/                   # Real Microscopy Data (44 files)
│   ├── Position_4_2.mdoc
│   ├── Position_4_2.mrc
│   ├── *.eer                          # EER files (41 files)
│   └── aretomo3.sh
│
└── 📊 Test_Input_3/                   # Real Microscopy Data (150+ files)
    ├── Position_*.mdoc                 # MDOC files (6 files)
    ├── Position_*.mrc                  # MRC files (6 files)
    ├── *.eer                          # EER files (140+ files)
    └── aretomo3.sh
```

---

## 🎯 **Test Categories**

### **🔍 Validation Tests**
**Purpose**: Verify basic functionality and imports
- **test_comprehensive_imports.py** - Import validation for all modules
- **Expected**: 88.9%+ success rate (8/9 modules)

### **🔧 Unit Tests**
**Purpose**: Test individual components in isolation
- **test_core_functionality.py** - Core system functionality
- **test_gui_basic.py** - GUI component creation and basic operations

### **🔗 Integration Tests**
**Purpose**: Test component interactions and full workflows
- **test_application_startup.py** - Full application startup and GUI creation
- **test_mrc_viewer.py** - MRC file handling with real 599.7 MB tomogram

### **📊 Real Data Tests**
**Purpose**: Validate with actual microscopy data
- **Test_Input_1/** - Complete tilt series with EER files
- **Test_Input_3/** - Multiple datasets for batch testing

---

## 🚀 **Running Tests**

### **Quick Test Run**
```bash
# Run all tests with comprehensive reporting
python run_tests.py
```

### **Individual Test Categories**
```bash
# Validation tests only
python tests/validation/test_comprehensive_imports.py

# Unit tests
python tests/unit/test_core_functionality.py
python tests/unit/test_gui_basic.py

# Integration tests
python tests/integration/test_application_startup.py
python tests/integration/test_mrc_viewer.py
```

### **Using Pytest**
```bash
# Run all pytest tests
pytest tests/

# Run specific category
pytest tests/unit/
pytest tests/integration/
pytest tests/validation/

# Verbose output
pytest tests/ -v

# With coverage
pytest tests/ --cov=aretomo3_gui
```

---

## 📊 **Expected Test Results**

### **Validation Tests**
```
🔬 AT3GUI Comprehensive Import Testing
============================================================
✅ Basic Imports..................... PASS
✅ Core Modules...................... PASS
✅ Configuration System.............. PASS
✅ GUI Components.................... PASS
✅ Viewer Components................. PASS (or PARTIAL)
✅ Widget Components................. PASS
✅ Utility Functions................. PASS
✅ Main Entry Point.................. PASS
✅ Critical Classes.................. PASS

🎯 Overall Success Rate: 8/9 (88.9%+)
```

### **Integration Tests**
```
📊 APPLICATION STARTUP TEST RESULTS
============================================================
✅ Main Module Import............... PASS
✅ GUI Class Creation............... PASS
✅ Theme System..................... PASS
✅ Configuration System............. PASS
✅ Logging System................... PASS
✅ File Operations.................. PASS
✅ MRC File Support................. PASS

🎯 Overall Success Rate: 7/7 (100%)
```

### **MRC Viewer Tests**
```
📊 MRC VIEWER & DATA HANDLING TEST RESULTS
============================================================
✅ MRC File Access.................. PASS
✅ mrcfile Library.................. PASS
✅ MRC Viewer Import................ PASS
✅ NumPy Operations................. PASS
✅ Matplotlib Integration........... PASS
✅ PyQt6 Widgets.................... PASS

🎯 Overall Success Rate: 6/6 (100%)
```

---

## 🔧 **Test Configuration**

### **Environment Setup**
```bash
# Ensure virtual environment is activated
source venv/bin/activate

# Install test dependencies
pip install -r requirements-dev.txt

# Set Python path
export PYTHONPATH=/path/to/AT3GUI:$PYTHONPATH
```

### **Test Data Requirements**
- **rec_TS_85.mrc** - 599.7 MB test tomogram (340 slices)
- **Test_Input_1/** - Complete tilt series dataset
- **Test_Input_3/** - Multiple position datasets

### **Display Configuration (for GUI tests)**
```bash
# For headless systems
export QT_QPA_PLATFORM=offscreen

# For X11 systems
export QT_QPA_PLATFORM=xcb

# For virtual display
xvfb-run -a python tests/integration/test_application_startup.py
```

---

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Import Errors**
```bash
# Ensure Python path is set correctly
export PYTHONPATH=/path/to/AT3GUI:$PYTHONPATH

# Check virtual environment
which python
pip list | grep PyQt6
```

#### **Display Issues (Return Code 132)**
```bash
# Use offscreen rendering
export QT_QPA_PLATFORM=offscreen

# Or use virtual display
sudo apt-get install xvfb
xvfb-run -a python run_tests.py
```

#### **Missing Test Data**
```bash
# Verify test files exist
ls -la rec_TS_85.mrc
ls -la tests/Test_Input_*/

# Check file permissions
chmod +r rec_TS_85.mrc
chmod -R +r tests/Test_Input_*/
```

### **Performance Issues**
```bash
# Increase memory limits
ulimit -v 8388608  # 8GB virtual memory

# Set thread limits
export OMP_NUM_THREADS=4
export MKL_NUM_THREADS=4
```

---

## 📈 **Test Metrics**

### **Success Criteria**
- **Validation Tests**: 85%+ pass rate
- **Unit Tests**: 90%+ pass rate
- **Integration Tests**: 80%+ pass rate
- **Overall Suite**: 85%+ pass rate

### **Performance Benchmarks**
- **Import Tests**: < 30 seconds
- **Application Startup**: < 45 seconds
- **MRC File Loading**: < 60 seconds
- **Full Test Suite**: < 5 minutes

### **Quality Gates**
- **No critical failures** in core functionality
- **GUI tests pass** on target platforms
- **Large file handling** works correctly
- **Memory usage** within acceptable limits

---

## 🎉 **Test Suite Features**

### **Comprehensive Coverage**
- ✅ **Import validation** for all modules
- ✅ **Core functionality** testing
- ✅ **GUI component** validation
- ✅ **Real data handling** with large files
- ✅ **Cross-platform** compatibility

### **Professional Reporting**
- ✅ **Detailed test results** with timing
- ✅ **Success rate calculations**
- ✅ **Error reporting** and diagnostics
- ✅ **Performance metrics**

### **Easy Execution**
- ✅ **Single command** test runner
- ✅ **Category-specific** testing
- ✅ **Pytest integration**
- ✅ **CI/CD ready** structure

**Run `python run_tests.py` to execute the complete test suite!** 🚀
