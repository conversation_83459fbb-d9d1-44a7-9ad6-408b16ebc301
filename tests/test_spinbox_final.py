#!/usr/bin/env python3
"""
Final test script to verify spinbox up/down arrows are visible and functional.
This test creates a simple window with both themed and unthemed spinboxes for comparison.
"""

import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QSpinBox, QDoubleSpinBox, QLabel, QPushButton,
                             QGroupBox)
from PyQt6.QtCore import Qt

# Import the theme manager
try:
    from src.gui.theme_manager import ThemeManager
except ImportError:
    try:
        from aretomo3_gui.gui.theme_manager import ThemeManager
    except ImportError:
        print("Could not import ThemeManager")
        ThemeManager = None

class SpinboxTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager() if ThemeManager else None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("AreTomo3 Spinbox Test - Arrows Visibility Check")
        self.setGeometry(100, 100, 600, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Add theme toggle button
        if self.theme_manager:
            theme_layout = QHBoxLayout()
            self.theme_button = QPushButton("Toggle Theme (Current: Light)")
            self.theme_button.clicked.connect(self.toggle_theme)
            theme_layout.addWidget(self.theme_button)
            theme_layout.addStretch()
            layout.addLayout(theme_layout)
        
        # Create themed spinboxes group
        themed_group = QGroupBox("Themed Spinboxes (Should have visible arrows)")
        themed_layout = QVBoxLayout(themed_group)
        
        # Integer spinbox
        int_layout = QHBoxLayout()
        int_layout.addWidget(QLabel("Integer Spinbox:"))
        self.int_spinbox = QSpinBox()
        self.int_spinbox.setMinimum(-100)
        self.int_spinbox.setMaximum(100)
        self.int_spinbox.setValue(0)
        int_layout.addWidget(self.int_spinbox)
        int_layout.addStretch()
        themed_layout.addLayout(int_layout)
        
        # Double spinbox
        double_layout = QHBoxLayout()
        double_layout.addWidget(QLabel("Double Spinbox:"))
        self.double_spinbox = QDoubleSpinBox()
        self.double_spinbox.setMinimum(-100.0)
        self.double_spinbox.setMaximum(100.0)
        self.double_spinbox.setValue(0.0)
        self.double_spinbox.setDecimals(2)
        double_layout.addWidget(self.double_spinbox)
        double_layout.addStretch()
        themed_layout.addLayout(double_layout)
        
        layout.addWidget(themed_group)
        
        # Create unthemed spinboxes group for comparison
        unthemed_group = QGroupBox("Unthemed Spinboxes (System default)")
        unthemed_layout = QVBoxLayout(unthemed_group)
        
        # Unthemed integer spinbox
        unthemed_int_layout = QHBoxLayout()
        unthemed_int_layout.addWidget(QLabel("Integer Spinbox:"))
        self.unthemed_int_spinbox = QSpinBox()
        self.unthemed_int_spinbox.setMinimum(-100)
        self.unthemed_int_spinbox.setMaximum(100)
        self.unthemed_int_spinbox.setValue(0)
        self.unthemed_int_spinbox.setStyleSheet("")  # Clear any theme styling
        unthemed_int_layout.addWidget(self.unthemed_int_spinbox)
        unthemed_int_layout.addStretch()
        unthemed_layout.addLayout(unthemed_int_layout)
        
        # Unthemed double spinbox
        unthemed_double_layout = QHBoxLayout()
        unthemed_double_layout.addWidget(QLabel("Double Spinbox:"))
        self.unthemed_double_spinbox = QDoubleSpinBox()
        self.unthemed_double_spinbox.setMinimum(-100.0)
        self.unthemed_double_spinbox.setMaximum(100.0)
        self.unthemed_double_spinbox.setValue(0.0)
        self.unthemed_double_spinbox.setDecimals(2)
        self.unthemed_double_spinbox.setStyleSheet("")  # Clear any theme styling
        unthemed_double_layout.addWidget(self.unthemed_double_spinbox)
        unthemed_double_layout.addStretch()
        unthemed_layout.addLayout(unthemed_double_layout)
        
        layout.addWidget(unthemed_group)
        
        # Instructions
        instructions = QLabel(
            "Instructions:\n"
            "1. Check if the themed spinboxes show up/down arrow buttons\n"
            "2. Test clicking the arrows to increment/decrement values\n"
            "3. Compare with system default spinboxes below\n"
            "4. Toggle between light and dark themes to test both\n"
            "5. Arrows should be visible and functional in both themes"
        )
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Apply initial theme
        if self.theme_manager:
            self.apply_current_theme()
            
    def toggle_theme(self):
        if not self.theme_manager:
            return
            
        # Toggle theme
        if self.theme_manager.current_theme == "light":
            self.theme_manager.set_theme("dark")
            self.theme_button.setText("Toggle Theme (Current: Dark)")
        else:
            self.theme_manager.set_theme("light")
            self.theme_button.setText("Toggle Theme (Current: Light)")
        
        self.apply_current_theme()
        
    def apply_current_theme(self):
        if not self.theme_manager:
            return
            
        stylesheet = self.theme_manager.get_theme_stylesheet()
        self.setStyleSheet(stylesheet)
        
        # Keep unthemed spinboxes without styling for comparison
        self.unthemed_int_spinbox.setStyleSheet("")
        self.unthemed_double_spinbox.setStyleSheet("")

def main():
    app = QApplication(sys.argv)
    
    # Create and show the test window
    window = SpinboxTestWindow()
    window.show()
    
    print("Spinbox Arrow Test Window opened.")
    print("Please check if the themed spinboxes display up/down arrow buttons.")
    print("The arrows should be visible on the right side of the spinbox controls.")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
