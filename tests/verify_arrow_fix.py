#!/usr/bin/env python3
"""Simple test to verify the theme manager imports and CSS is valid."""

import sys
import os

# Add the src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from gui.theme_manager import ThemeManager
    print("✓ Successfully imported ThemeManager from src/gui/")
    
    # Test theme manager
    tm = ThemeManager()
    print(f"✓ ThemeManager created, current theme: {tm.current_theme}")
    
    # Test dark theme CSS
    tm.set_theme("dark")
    dark_css = tm.get_theme_stylesheet()
    print(f"✓ Dark theme CSS generated ({len(dark_css)} characters)")
    
    # Check for arrow definitions in dark theme
    if "QSpinBox::up-arrow" in dark_css and "QSpinBox::down-arrow" in dark_css:
        print("✓ Dark theme contains arrow definitions")
    else:
        print("✗ Dark theme missing arrow definitions")
    
    # Test light theme CSS
    tm.set_theme("light")
    light_css = tm.get_theme_stylesheet()
    print(f"✓ Light theme CSS generated ({len(light_css)} characters)")
    
    # Check for arrow definitions in light theme
    if "QSpinBox::up-arrow" in light_css and "QSpinBox::down-arrow" in light_css:
        print("✓ Light theme contains arrow definitions")
    else:
        print("✗ Light theme missing arrow definitions")
        
    print("\n" + "="*60)
    print("ARROW CSS SAMPLE FROM DARK THEME:")
    print("="*60)
    
    # Extract and show arrow CSS
    lines = dark_css.split('\n')
    in_arrow_section = False
    for line in lines:
        if "QSpinBox::up-arrow" in line or "QSpinBox::down-arrow" in line:
            in_arrow_section = True
        if in_arrow_section:
            print(line)
        if in_arrow_section and line.strip() == "}":
            in_arrow_section = False
            print()
            
except ImportError as e:
    print(f"✗ Import error: {e}")
    
    # Try alternative path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aretomo3_gui'))
    try:
        from gui.theme_manager import ThemeManager
        print("✓ Successfully imported ThemeManager from aretomo3_gui/gui/")
        
        tm = ThemeManager()
        print(f"✓ ThemeManager created, current theme: {tm.current_theme}")
        
        # Test arrow definitions
        tm.set_theme("dark")
        dark_css = tm.get_theme_stylesheet()
        if "QSpinBox::up-arrow" in dark_css:
            print("✓ Alternative theme manager has arrow definitions")
        else:
            print("✗ Alternative theme manager missing arrow definitions")
            
    except ImportError as e2:
        print(f"✗ Both import attempts failed: {e2}")

print("\n" + "="*60)
print("FIX SUMMARY:")
print("="*60)
print("The spinbox arrow visibility issue has been fixed by adding:")
print("1. QSpinBox::up-arrow and QSpinBox::down-arrow CSS definitions")
print("2. CSS triangular arrows using border properties")
print("3. Proper color schemes for both dark and light themes")
print("4. Hover effects for better user interaction")
print("\nThe black rectangles should now be replaced with visible triangular arrows.")
