#!/bin/bash
 
files="/mnt/HDD/ak_devel/AT3Gui/tests/Test_Input_1/" # All frames with mdocs in this folder will be processed. Can also specify only one Position_X tilt series.
gain_ref="GainReference.gain" # give the gainref file!
outdir=aretomo_output
apix=1.91
tilt_axis=-95.75
dark_tol=0.7
vol_z=2048
amp_con=0.1
fm_dose=0.14 # dose per frame! so 3.5 e/A² per 25 slices here
lowpass=15
# align_z=1400 # Not really needed since AreTomo3 can estimate it automatically
 
ml purge
ml IMOD
ml AreTomo3
 
mkdir -p ${outdir}
 
time AreTomo3 \
    -InPrefix "${files}" \
    -Insuffix ".mdoc" \
    -Gain "${gain_ref}" \
    -OutDir "${outdir}" \
    -FlipGain 1 \
    -Gpu ${CUDA_VISIBLE_DEVICES} \
    -PixSize ${apix} \
    -McBin 1 \
    -McPatch 1 1 \
    -FmInt 12 \
    -FmDose ${fm_dose} \
    -SplitSum 1 \
    -VolZ ${vol_z} \
    -TiltAxis ${tilt_axis} \
    -AtBin 4 \
    -OutXF 1 \
    -OutImod 1 \
    -Wbp 1 \
    -FlipVol 1 \
    -TiltCor 1 \
    -Patch 0 0 \
    -DarkTol ${dark_tol} \
    -CorrCTF 1 ${lowpass} \
    -Kv 300 \
    -Cs 2.7 \
    -AmpContrast ${amp_con}

