@echo off
REM AT3GUI Setup Script for Windows
REM Automated installation and setup for AreTomo3 GUI

setlocal enabledelayedexpansion

echo 🚀 AT3GUI Setup Script for Windows
echo ===================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ℹ️  Found Python %PYTHON_VERSION%

REM Check if we're in the right directory
if not exist "aretomo3_gui\main.py" (
    echo ❌ Please run this script from the AT3GUI root directory
    echo Expected structure: AT3GUI\aretomo3_gui\main.py
    pause
    exit /b 1
)

echo ✅ AT3GUI directory structure verified

REM Create virtual environment
if not exist "venv" (
    echo ℹ️  Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created
) else (
    echo ⚠️  Virtual environment already exists
)

REM Activate virtual environment
echo ℹ️  Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)

REM Upgrade pip
echo ℹ️  Upgrading pip...
python -m pip install --upgrade pip setuptools wheel
if errorlevel 1 (
    echo ❌ Failed to upgrade pip
    pause
    exit /b 1
)

REM Install dependencies
echo ℹ️  Installing AT3GUI dependencies...
if exist "requirements.txt" (
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
) else (
    echo ❌ requirements.txt not found
    pause
    exit /b 1
)

REM Test installation
echo ℹ️  Testing AT3GUI installation...
python -c "import aretomo3_gui; print('✅ Import successful')" 2>nul
if errorlevel 1 (
    echo ❌ AT3GUI installation test failed
    pause
    exit /b 1
)
echo ✅ AT3GUI installation test passed

REM Create launcher batch file
echo ℹ️  Creating launcher script...
(
echo @echo off
echo cd /d "%~dp0"
echo call venv\Scripts\activate.bat
echo python launch_at3gui.py
echo pause
) > launch_at3gui.bat

echo ✅ Launcher script created

REM Success message
echo.
echo 🎉 AT3GUI Setup Complete!
echo ========================
echo.
echo ✅ Installation successful
echo ℹ️  To launch AT3GUI:
echo   1. Double-click launch_at3gui.bat
echo   2. OR run: python launch_at3gui.py
echo   3. OR run: python -m aretomo3_gui.main
echo.
echo ℹ️  Test with sample data: rec_TS_85.mrc (599.7 MB tomogram)
echo.

REM Ask if user wants to launch now
set /p LAUNCH="Would you like to launch AT3GUI now? (y/n): "
if /i "%LAUNCH%"=="y" (
    echo ℹ️  Launching AT3GUI...
    python launch_at3gui.py
)

echo.
echo Setup script completed successfully!
pause
