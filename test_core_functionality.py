#!/usr/bin/env python3
"""
Core functionality tests for AT3GUI.
Tests logging, configuration, error handling, and system monitoring.
"""

import sys
import os
import tempfile
import logging
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/mnt/HDD/ak_devel/AT3Gui')

def test_logging_system():
    """Test the logging configuration system."""
    print("🧪 Testing Logging System...")
    
    try:
        from aretomo3_gui.core.logging_config import setup_logging
        
        # Test basic setup
        setup_logging()
        logger = logging.getLogger("test_logger")
        
        # Test different log levels
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        
        print("   ✅ Logging system initialized successfully")
        print("   ✅ All log levels working")
        return True
        
    except Exception as e:
        print(f"   ❌ Logging system failed: {e}")
        return False

def test_configuration_system():
    """Test the configuration management system."""
    print("\n🧪 Testing Configuration System...")
    
    try:
        from aretomo3_gui.core.config.config_validation import AreTomo3Config
        
        # Test basic configuration creation
        config = AreTomo3Config(pixel_size=1.91)
        print("   ✅ Basic configuration created")
        
        # Test parameter validation
        if hasattr(config, 'pixel_size') and config.pixel_size == 1.91:
            print("   ✅ Parameter validation working")
        else:
            print("   ⚠️  Parameter validation may have issues")
        
        # Test configuration manager
        from aretomo3_gui.core.config.config_manager import ConfigManager
        config_manager = ConfigManager()
        print("   ✅ Configuration manager initialized")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration system failed: {e}")
        return False

def test_error_handling():
    """Test the error handling system."""
    print("\n🧪 Testing Error Handling System...")
    
    try:
        from aretomo3_gui.core.error_handling import FileSystemError
        
        # Test custom exception creation
        try:
            raise FileSystemError("Test error message")
        except FileSystemError as e:
            print("   ✅ Custom exceptions working")
        
        # Test error handling utilities
        from aretomo3_gui.core import error_handling
        if hasattr(error_handling, 'handle_exception'):
            print("   ✅ Error handling utilities available")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error handling system failed: {e}")
        return False

def test_system_monitoring():
    """Test the system monitoring capabilities."""
    print("\n🧪 Testing System Monitoring...")
    
    try:
        from aretomo3_gui.core.system_monitor import SystemMonitor
        
        # Test system monitor creation
        monitor = SystemMonitor(update_interval=1.0)
        print("   ✅ System monitor created")
        
        # Test basic monitoring
        if hasattr(monitor, 'start') and hasattr(monitor, 'stop'):
            print("   ✅ Monitor control methods available")
        
        # Test GPU monitoring
        from aretomo3_gui.core.system_monitor import GPUMonitor
        gpu_monitor = GPUMonitor()
        print("   ✅ GPU monitor created")
        
        return True
        
    except Exception as e:
        print(f"   ❌ System monitoring failed: {e}")
        return False

def test_thread_management():
    """Test the thread management system."""
    print("\n🧪 Testing Thread Management...")
    
    try:
        from aretomo3_gui.core.thread_manager import get_thread_manager, TaskPriority
        
        # Test thread manager access
        thread_manager = get_thread_manager()
        print("   ✅ Thread manager accessible")
        
        # Test task priority enum
        if hasattr(TaskPriority, 'HIGH') and hasattr(TaskPriority, 'LOW'):
            print("   ✅ Task priority system available")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Thread management failed: {e}")
        return False

def test_file_operations():
    """Test file watching and resource management."""
    print("\n🧪 Testing File Operations...")
    
    try:
        from aretomo3_gui.core.file_watcher import FileWatcher
        
        # Test file watcher creation
        watcher = FileWatcher()
        print("   ✅ File watcher created")
        
        # Test resource manager
        from aretomo3_gui.core.resource_manager import ResourceManager
        resource_manager = ResourceManager()
        print("   ✅ Resource manager created")
        
        return True
        
    except Exception as e:
        print(f"   ❌ File operations failed: {e}")
        return False

def test_dependency_checking():
    """Test dependency validation."""
    print("\n🧪 Testing Dependency Checking...")
    
    try:
        from aretomo3_gui.core.dependency_check import check_dependencies
        
        # Test dependency checking
        deps_ok = check_dependencies()
        if deps_ok:
            print("   ✅ All dependencies satisfied")
        else:
            print("   ⚠️  Some dependencies missing (expected in dev environment)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Dependency checking failed: {e}")
        return False

def test_utility_functions():
    """Test utility functions."""
    print("\n🧪 Testing Utility Functions...")
    
    try:
        # Test MDOC parser
        from aretomo3_gui.utils.mdoc_parser import parse_mdoc
        print("   ✅ MDOC parser available")
        
        # Test export functions
        from aretomo3_gui.utils.export_functions import export_to_csv
        print("   ✅ Export functions available")
        
        # Test general utilities
        from aretomo3_gui.utils.utils import format_file_size
        
        # Test file size formatting
        size_str = format_file_size(1024 * 1024)  # 1 MB
        if "MB" in size_str or "1.0" in size_str:
            print("   ✅ Utility functions working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Utility functions failed: {e}")
        return False

def main():
    """Run comprehensive core functionality tests."""
    print("🔬 AT3GUI Core Functionality Testing")
    print("=" * 60)
    
    test_results = []
    
    # Execute all test phases
    test_results.append(("Logging System", test_logging_system()))
    test_results.append(("Configuration System", test_configuration_system()))
    test_results.append(("Error Handling", test_error_handling()))
    test_results.append(("System Monitoring", test_system_monitoring()))
    test_results.append(("Thread Management", test_thread_management()))
    test_results.append(("File Operations", test_file_operations()))
    test_results.append(("Dependency Checking", test_dependency_checking()))
    test_results.append(("Utility Functions", test_utility_functions()))
    
    # Calculate results
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    success_rate = (passed / total) * 100
    
    print(f"\n{'='*60}")
    print("📊 CORE FUNCTIONALITY TEST RESULTS")
    print(f"{'='*60}")
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\n🎯 Overall Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 95:
        print("🎉 EXCELLENT! Core functionality is rock solid!")
        return 0
    elif success_rate >= 85:
        print("✅ GOOD! Core systems working well.")
        return 0
    elif success_rate >= 75:
        print("⚠️  ACCEPTABLE! Some core issues to address.")
        return 1
    else:
        print("❌ CRITICAL! Major core functionality failures.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
