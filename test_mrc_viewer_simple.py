#!/usr/bin/env python3
"""
Simple test for MRC viewer functionality with the 3D tomogram.
"""

import sys
import os
import mrcfile
import numpy as np
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/mnt/HDD/ak_devel/AT3Gui')

def test_mrc_file_detailed(file_path):
    """Detailed analysis of the MRC file."""
    print(f"\n{'='*70}")
    print(f"DETAILED MRC FILE ANALYSIS: {os.path.basename(file_path)}")
    print(f"{'='*70}")
    
    try:
        with mrcfile.open(file_path, permissive=True) as mrc:
            print(f"✓ File opened successfully")
            print(f"  📁 File path: {file_path}")
            print(f"  📊 Data shape: {mrc.data.shape}")
            print(f"  🔢 Data type: {mrc.data.dtype}")
            print(f"  📏 Voxel size: {mrc.voxel_size.x:.3f} x {mrc.voxel_size.y:.3f} x {mrc.voxel_size.z:.3f} Å")
            print(f"  📈 Value range: {mrc.data.min():.3f} to {mrc.data.max():.3f}")
            print(f"  📊 Mean: {mrc.data.mean():.3f}, Std: {mrc.data.std():.3f}")
            
            # Check if it's a 3D volume
            if len(mrc.data.shape) == 3:
                nz, ny, nx = mrc.data.shape
                print(f"  ✅ 3D tomogram confirmed:")
                print(f"     • Z-slices: {nz}")
                print(f"     • Y-dimension: {ny}")
                print(f"     • X-dimension: {nx}")
                print(f"     • Total voxels: {nx * ny * nz:,}")
                
                # Analyze slice statistics
                print(f"\n  📊 Slice-by-slice analysis:")
                slice_means = [mrc.data[i].mean() for i in range(min(10, nz))]
                slice_stds = [mrc.data[i].std() for i in range(min(10, nz))]
                
                for i in range(min(10, nz)):
                    print(f"     Slice {i:3d}: mean={slice_means[i]:7.3f}, std={slice_stds[i]:7.3f}")
                
                if nz > 10:
                    print(f"     ... (showing first 10 of {nz} slices)")
                
                # Test different slice positions
                print(f"\n  🎯 Testing key slice positions:")
                test_slices = [0, nz//4, nz//2, 3*nz//4, nz-1]
                for slice_idx in test_slices:
                    if slice_idx < nz:
                        slice_data = mrc.data[slice_idx]
                        print(f"     Slice {slice_idx:3d} ({slice_idx/nz*100:5.1f}%): "
                              f"min={slice_data.min():7.3f}, max={slice_data.max():7.3f}, "
                              f"mean={slice_data.mean():7.3f}")
                
                # Check for interesting features
                print(f"\n  🔍 Feature analysis:")
                
                # Find slices with highest and lowest contrast
                contrasts = [mrc.data[i].std() for i in range(nz)]
                max_contrast_slice = np.argmax(contrasts)
                min_contrast_slice = np.argmin(contrasts)
                
                print(f"     • Highest contrast slice: {max_contrast_slice} (std={contrasts[max_contrast_slice]:.3f})")
                print(f"     • Lowest contrast slice: {min_contrast_slice} (std={contrasts[min_contrast_slice]:.3f})")
                
                # Find slices with extreme values
                slice_maxes = [mrc.data[i].max() for i in range(nz)]
                slice_mins = [mrc.data[i].min() for i in range(nz)]
                brightest_slice = np.argmax(slice_maxes)
                darkest_slice = np.argmin(slice_mins)
                
                print(f"     • Brightest slice: {brightest_slice} (max value={slice_maxes[brightest_slice]:.3f})")
                print(f"     • Darkest slice: {darkest_slice} (min value={slice_mins[darkest_slice]:.3f})")
                
                return True, {
                    'shape': mrc.data.shape,
                    'dtype': mrc.data.dtype,
                    'voxel_size': (mrc.voxel_size.x, mrc.voxel_size.y, mrc.voxel_size.z),
                    'value_range': (mrc.data.min(), mrc.data.max()),
                    'mean': mrc.data.mean(),
                    'std': mrc.data.std(),
                    'max_contrast_slice': max_contrast_slice,
                    'brightest_slice': brightest_slice,
                    'darkest_slice': darkest_slice
                }
            else:
                print(f"  ⚠ Not a 3D volume (shape: {mrc.data.shape})")
                return False, None
                
    except Exception as e:
        print(f"✗ Error reading MRC file: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_viewer_recommendations(file_info):
    """Provide recommendations for optimal viewer settings."""
    if not file_info:
        return
        
    print(f"\n{'='*70}")
    print("🎯 VIEWER OPTIMIZATION RECOMMENDATIONS")
    print(f"{'='*70}")
    
    shape = file_info['shape']
    value_range = file_info['value_range']
    
    print(f"📋 Optimal viewer settings for this tomogram:")
    print(f"   • Recommended slice range: 0 to {shape[0]-1}")
    print(f"   • Interesting slices to view:")
    print(f"     - Start: slice 0")
    print(f"     - Quarter: slice {shape[0]//4}")
    print(f"     - Middle: slice {shape[0]//2}")
    print(f"     - Three-quarter: slice {3*shape[0]//4}")
    print(f"     - End: slice {shape[0]-1}")
    print(f"     - Highest contrast: slice {file_info['max_contrast_slice']}")
    print(f"     - Brightest features: slice {file_info['brightest_slice']}")
    
    print(f"\n   • Contrast settings:")
    print(f"     - Full range: {value_range[0]:.3f} to {value_range[1]:.3f}")
    print(f"     - Recommended min: {value_range[0] + 0.1 * (value_range[1] - value_range[0]):.3f}")
    print(f"     - Recommended max: {value_range[1] - 0.1 * (value_range[1] - value_range[0]):.3f}")
    
    print(f"\n   • Recommended colormaps:")
    print(f"     - For general viewing: 'gray' or 'viridis'")
    print(f"     - For feature detection: 'plasma' or 'inferno'")
    print(f"     - For publication: 'gray'")
    
    print(f"\n   • View modes to try:")
    print(f"     - 2D Slice View: Best for detailed examination")
    print(f"     - Multi-slice Grid: Good for overview")
    print(f"     - Orthogonal Views: Useful for 3D understanding")

def main():
    """Main test function."""
    print("🔬 AT3GUI Enhanced Viewer - MRC File Analysis")
    print("=" * 70)
    
    # Test file
    test_file = "/mnt/HDD/ak_devel/AT3Gui/rec_TS_85.mrc"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return 1
    
    # Detailed analysis
    success, file_info = test_mrc_file_detailed(test_file)
    
    if not success:
        print("❌ MRC file analysis failed")
        return 1
    
    # Provide viewer recommendations
    test_viewer_recommendations(file_info)
    
    print(f"\n{'='*70}")
    print("✅ MRC FILE ANALYSIS COMPLETE!")
    print("🎯 The enhanced viewer is ready to display this tomogram with:")
    print("   • 340 slices to navigate through")
    print("   • Rich contrast and colormap options")
    print("   • Measurement tools for analysis")
    print("   • Export capabilities for publication")
    print(f"{'='*70}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
