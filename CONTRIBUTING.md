# Contributing to AreTomo3 GUI

First off, thank you for considering contributing to AreTomo3 GUI! It's people like you that make AreTomo3 GUI such a great tool.

## Code of Conduct

This project and everyone participating in it is governed by our [Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code.

## How Can I Contribute?

### Reporting Bugs

Before creating bug reports, please check [this list](#before-submitting-a-bug-report) as you might find out that you don't need to create one. When you are creating a bug report, please [include as many details as possible](#how-do-i-submit-a-good-bug-report).

#### Before Submitting A Bug Report

* Check the documentation for a list of common questions and problems.
* Check that your issue isn't already filed.
* Check that you're using the latest version.

#### How Do I Submit A Good Bug Report?

Bugs are tracked as [GitHub issues](https://github.com/aretomo3/aretomo3-gui/issues). Create an issue and provide the following information:

* Use a clear and descriptive title
* Describe the exact steps which reproduce the problem
* Provide specific examples to demonstrate the steps
* Describe the behavior you observed after following the steps
* Explain which behavior you expected to see instead and why
* Include screenshots and animated GIFs if possible
* Include your environment details (OS, Python version, etc.)

### Suggesting Enhancements

Enhancement suggestions are also tracked as [GitHub issues](https://github.com/aretomo3/aretomo3-gui/issues). When creating an enhancement suggestion, please include:

* A clear and descriptive title
* A detailed description of the proposed functionality
* Any possible drawbacks
* Screenshots or sketches if applicable

### Pull Requests

* Fill in [the required template](pull_request_template.md)
* Do not include issue numbers in the PR title
* Include screenshots and animated GIFs in your pull request whenever possible
* Follow our [coding standards](#coding-standards)
* Document new code
* End all files with a newline

## Development Process

1. Fork the repo
2. Create a new branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run the tests (`pytest`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## Coding Standards

* Use [Black](https://github.com/psf/black) for code formatting
* Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/)
* Write docstrings for all public methods (Google style)
* Include type hints
* Add tests for new functionality
* Update documentation when needed

## Setting Up Development Environment

1. Clone the repository
```bash
git clone https://github.com/yourusername/aretomo3-gui.git
cd aretomo3-gui
```

2. Create a virtual environment
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows
```

3. Install development dependencies
```bash
pip install -r requirements-dev.txt
```

4. Install pre-commit hooks
```bash
pre-commit install
```

## Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=aretomo3_gui

# Run specific test file
pytest tests/test_specific.py
