#!/usr/bin/env python3
"""
Simple test script to check spinbox arrow visibility
"""
import sys
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QSpinBox, QDoubleSpinBox, QLabel

class SpinBoxTest(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        
        # Test QSpinBox
        layout.addWidget(QLabel("QSpinBox:"))
        spinbox = QSpinBox()
        spinbox.setRange(0, 100)
        spinbox.setValue(50)
        layout.addWidget(spinbox)
        
        # Test QDoubleSpinBox  
        layout.addWidget(QLabel("QDoubleSpinBox:"))
        double_spinbox = QDoubleSpinBox()
        double_spinbox.setRange(0.0, 100.0)
        double_spinbox.setValue(50.0)
        layout.addWidget(double_spinbox)
        
        # Test with fixed width (like in the main app)
        layout.addWidget(QLabel("Fixed Width QSpinBox:"))
        fixed_spinbox = QSpinBox()
        fixed_spinbox.setRange(0, 100)
        fixed_spinbox.setValue(50)
        fixed_spinbox.setFixedWidth(80)
        layout.addWidget(fixed_spinbox)
        
        self.setLayout(layout)
        self.setWindowTitle('SpinBox Arrow Test')
        self.resize(300, 200)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = SpinBoxTest()
    window.show()
    sys.exit(app.exec())
