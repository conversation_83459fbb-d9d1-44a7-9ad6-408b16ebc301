#!/usr/bin/env python3
"""
Comprehensive import tests for AT3GUI after cleanup and reorganization.
Tests all critical modules to ensure imports work correctly.
"""

import sys
import os
import traceback
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/mnt/HDD/ak_devel/AT3Gui')

def test_basic_imports():
    """Test basic package imports."""
    print("🧪 Testing Basic Package Imports...")
    
    try:
        import aretomo3_gui
        print("   ✅ aretomo3_gui package imported successfully")
        
        from aretomo3_gui import __version__
        print(f"   ✅ Package version: {__version__}")
        
    except Exception as e:
        print(f"   ❌ Basic import failed: {e}")
        return False
    
    return True

def test_core_imports():
    """Test core module imports."""
    print("\n🧪 Testing Core Module Imports...")
    
    core_modules = [
        ("aretomo3_gui.core.logging_config", "Logging configuration"),
        ("aretomo3_gui.core.error_handling", "Error handling"),
        ("aretomo3_gui.core.system_monitor", "System monitoring"),
        ("aretomo3_gui.core.thread_manager", "Thread management"),
        ("aretomo3_gui.core.file_watcher", "File watching"),
        ("aretomo3_gui.core.resource_manager", "Resource management"),
        ("aretomo3_gui.core.dependency_check", "Dependency checking"),
    ]
    
    success_count = 0
    for module_name, description in core_modules:
        try:
            __import__(module_name)
            print(f"   ✅ {description}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ {description}: {e}")
    
    print(f"\n   📊 Core imports: {success_count}/{len(core_modules)} successful")
    return success_count == len(core_modules)

def test_config_imports():
    """Test configuration system imports."""
    print("\n🧪 Testing Configuration System Imports...")
    
    config_modules = [
        ("aretomo3_gui.core.config.config", "Main configuration"),
        ("aretomo3_gui.core.config.config_manager", "Configuration manager"),
        ("aretomo3_gui.core.config.config_validation", "Configuration validation"),
        ("aretomo3_gui.core.config.template_manager", "Template manager"),
    ]
    
    success_count = 0
    for module_name, description in config_modules:
        try:
            __import__(module_name)
            print(f"   ✅ {description}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ {description}: {e}")
    
    print(f"\n   📊 Config imports: {success_count}/{len(config_modules)} successful")
    return success_count == len(config_modules)

def test_gui_imports():
    """Test GUI component imports."""
    print("\n🧪 Testing GUI Component Imports...")
    
    gui_modules = [
        ("aretomo3_gui.gui.main_window", "Main window"),
        ("aretomo3_gui.gui.theme_manager", "Theme manager"),
        ("aretomo3_gui.gui.advanced_settings_tab", "Advanced settings"),
    ]
    
    success_count = 0
    for module_name, description in gui_modules:
        try:
            __import__(module_name)
            print(f"   ✅ {description}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ {description}: {e}")
            traceback.print_exc()
    
    print(f"\n   📊 GUI imports: {success_count}/{len(gui_modules)} successful")
    return success_count == len(gui_modules)

def test_viewer_imports():
    """Test viewer component imports."""
    print("\n🧪 Testing Viewer Component Imports...")
    
    viewer_modules = [
        ("aretomo3_gui.gui.viewers.mrc_viewer", "MRC viewer"),
        ("aretomo3_gui.gui.viewers.analysis_viewer", "Analysis viewer"),
        ("aretomo3_gui.gui.viewers.preview_grid", "Preview grid"),
        ("aretomo3_gui.gui.viewers.visualization", "Visualization tools"),
    ]
    
    success_count = 0
    for module_name, description in viewer_modules:
        try:
            __import__(module_name)
            print(f"   ✅ {description}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ {description}: {e}")
    
    print(f"\n   📊 Viewer imports: {success_count}/{len(viewer_modules)} successful")
    return success_count >= len(viewer_modules) * 0.8  # 80% success rate acceptable

def test_widget_imports():
    """Test widget component imports."""
    print("\n🧪 Testing Widget Component Imports...")
    
    widget_modules = [
        ("aretomo3_gui.gui.widgets.batch_processing", "Batch processing"),
        ("aretomo3_gui.gui.widgets.resource_monitor", "Resource monitor"),
    ]
    
    success_count = 0
    for module_name, description in widget_modules:
        try:
            __import__(module_name)
            print(f"   ✅ {description}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ {description}: {e}")
    
    print(f"\n   📊 Widget imports: {success_count}/{len(widget_modules)} successful")
    return success_count == len(widget_modules)

def test_utility_imports():
    """Test utility function imports."""
    print("\n🧪 Testing Utility Function Imports...")
    
    utility_modules = [
        ("aretomo3_gui.utils.export_functions", "Export functions"),
        ("aretomo3_gui.utils.mdoc_parser", "MDOC parser"),
        ("aretomo3_gui.utils.utils", "General utilities"),
    ]
    
    success_count = 0
    for module_name, description in utility_modules:
        try:
            __import__(module_name)
            print(f"   ✅ {description}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ {description}: {e}")
    
    print(f"\n   📊 Utility imports: {success_count}/{len(utility_modules)} successful")
    return success_count == len(utility_modules)

def test_main_entry_point():
    """Test main entry point import."""
    print("\n🧪 Testing Main Entry Point...")
    
    try:
        from aretomo3_gui.main import main
        print("   ✅ Main entry point imported successfully")
        
        # Test that main function exists and is callable
        if callable(main):
            print("   ✅ Main function is callable")
            return True
        else:
            print("   ❌ Main function is not callable")
            return False
            
    except Exception as e:
        print(f"   ❌ Main entry point import failed: {e}")
        traceback.print_exc()
        return False

def test_critical_classes():
    """Test critical class imports."""
    print("\n🧪 Testing Critical Class Imports...")
    
    try:
        from aretomo3_gui.gui.main_window import AreTomoGUI
        print("   ✅ AreTomoGUI class imported")
        
        from aretomo3_gui.gui.theme_manager import ThemeManager
        print("   ✅ ThemeManager class imported")
        
        from aretomo3_gui.core.logging_config import setup_logging
        print("   ✅ setup_logging function imported")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Critical class import failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive import tests."""
    print("🔬 AT3GUI Comprehensive Import Testing")
    print("=" * 60)
    
    test_results = []
    
    # Execute all test phases
    test_results.append(("Basic Imports", test_basic_imports()))
    test_results.append(("Core Modules", test_core_imports()))
    test_results.append(("Configuration System", test_config_imports()))
    test_results.append(("GUI Components", test_gui_imports()))
    test_results.append(("Viewer Components", test_viewer_imports()))
    test_results.append(("Widget Components", test_widget_imports()))
    test_results.append(("Utility Functions", test_utility_imports()))
    test_results.append(("Main Entry Point", test_main_entry_point()))
    test_results.append(("Critical Classes", test_critical_classes()))
    
    # Calculate results
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    success_rate = (passed / total) * 100
    
    print(f"\n{'='*60}")
    print("📊 IMPORT TEST RESULTS SUMMARY")
    print(f"{'='*60}")
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\n🎯 Overall Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT! Import tests passed with flying colors!")
        return 0
    elif success_rate >= 80:
        print("✅ GOOD! Most imports working, minor issues to address.")
        return 0
    elif success_rate >= 70:
        print("⚠️  ACCEPTABLE! Some imports failing, needs attention.")
        return 1
    else:
        print("❌ CRITICAL! Major import failures, requires immediate fixing.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
