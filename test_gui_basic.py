#!/usr/bin/env python3
"""
Basic GUI component tests for AT3GUI.
Tests GUI initialization without actually showing windows.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/mnt/HDD/ak_devel/AT3Gui')

def test_theme_manager():
    """Test theme manager functionality."""
    print("🧪 Testing Theme Manager...")
    
    try:
        from aretomo3_gui.gui.theme_manager import ThemeManager
        
        # Test theme manager creation
        theme_manager = ThemeManager()
        print("   ✅ Theme manager created")
        
        # Test theme loading
        if hasattr(theme_manager, 'get_theme_stylesheet'):
            stylesheet = theme_manager.get_theme_stylesheet()
            if stylesheet and len(stylesheet) > 0:
                print("   ✅ Theme stylesheet loaded")
            else:
                print("   ⚠️  Theme stylesheet empty")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Theme manager failed: {e}")
        return False

def test_advanced_settings():
    """Test advanced settings tab."""
    print("\n🧪 Testing Advanced Settings Tab...")
    
    try:
        from aretomo3_gui.gui.advanced_settings_tab import AdvancedSettingsTab
        
        # Test creation without parent (should work)
        settings_tab = AdvancedSettingsTab()
        print("   ✅ Advanced settings tab created")
        
        # Test settings retrieval
        if hasattr(settings_tab, 'get_settings'):
            settings = settings_tab.get_settings()
            if isinstance(settings, dict) and len(settings) > 0:
                print("   ✅ Settings retrieval working")
            else:
                print("   ⚠️  Settings retrieval may have issues")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Advanced settings failed: {e}")
        return False

def test_viewers():
    """Test viewer components."""
    print("\n🧪 Testing Viewer Components...")
    
    try:
        # Test MRC viewer
        from aretomo3_gui.gui.viewers.mrc_viewer import MRCViewer
        print("   ✅ MRC viewer imported")
        
        # Test analysis viewer
        from aretomo3_gui.gui.viewers.analysis_viewer import AnalysisViewer
        print("   ✅ Analysis viewer imported")
        
        # Test preview grid
        from aretomo3_gui.gui.viewers.preview_grid import PreviewGridView
        print("   ✅ Preview grid imported")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Viewer components failed: {e}")
        return False

def test_widgets():
    """Test widget components."""
    print("\n🧪 Testing Widget Components...")
    
    try:
        # Test batch processing widget
        from aretomo3_gui.gui.widgets.batch_processing import BatchProcessingWidget
        print("   ✅ Batch processing widget imported")
        
        # Test resource monitor
        from aretomo3_gui.gui.widgets.resource_monitor import ResourceMonitor
        print("   ✅ Resource monitor imported")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Widget components failed: {e}")
        return False

def test_main_window_class():
    """Test main window class without creating QApplication."""
    print("\n🧪 Testing Main Window Class...")
    
    try:
        from aretomo3_gui.gui.main_window import AreTomoGUI
        print("   ✅ Main window class imported")
        
        # Test TiltSeries class
        from aretomo3_gui.gui.main_window import TiltSeries
        tilt_series = TiltSeries("test_position")
        if hasattr(tilt_series, 'position_name') and tilt_series.position_name == "test_position":
            print("   ✅ TiltSeries class working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Main window class failed: {e}")
        return False

def test_file_operations():
    """Test file operation utilities."""
    print("\n🧪 Testing File Operations...")
    
    try:
        # Test MDOC parser
        from aretomo3_gui.utils.mdoc_parser import parse_mdoc
        print("   ✅ MDOC parser available")
        
        # Test export functions
        from aretomo3_gui.utils.export_functions import export_to_csv
        print("   ✅ Export functions available")
        
        return True
        
    except Exception as e:
        print(f"   ❌ File operations failed: {e}")
        return False

def test_configuration_validation():
    """Test configuration validation."""
    print("\n🧪 Testing Configuration Validation...")
    
    try:
        from aretomo3_gui.core.config.config_validation import AreTomo3Config
        
        # Test basic config creation
        config = AreTomo3Config(pixel_size=1.91)
        print("   ✅ Configuration validation working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration validation failed: {e}")
        return False

def main():
    """Run basic GUI component tests."""
    print("🔬 AT3GUI Basic GUI Component Testing")
    print("=" * 60)
    
    test_results = []
    
    # Execute all test phases
    test_results.append(("Theme Manager", test_theme_manager()))
    test_results.append(("Advanced Settings", test_advanced_settings()))
    test_results.append(("Viewer Components", test_viewers()))
    test_results.append(("Widget Components", test_widgets()))
    test_results.append(("Main Window Class", test_main_window_class()))
    test_results.append(("File Operations", test_file_operations()))
    test_results.append(("Configuration Validation", test_configuration_validation()))
    
    # Calculate results
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    success_rate = (passed / total) * 100
    
    print(f"\n{'='*60}")
    print("📊 BASIC GUI COMPONENT TEST RESULTS")
    print(f"{'='*60}")
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\n🎯 Overall Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT! GUI components are working great!")
        return 0
    elif success_rate >= 80:
        print("✅ GOOD! Most GUI components working.")
        return 0
    elif success_rate >= 70:
        print("⚠️  ACCEPTABLE! Some GUI issues to address.")
        return 1
    else:
        print("❌ CRITICAL! Major GUI component failures.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
