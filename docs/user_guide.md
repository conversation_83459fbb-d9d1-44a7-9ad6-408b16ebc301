# AreTomo3 GUI Documentation

## Introduction

AreTomo3 GUI is a graphical user interface for AreTomo3, designed to simplify the process of tilt series alignment and reconstruction in cryo-electron microscopy.

## Installation

Please refer to the [README.md](../README.md) for installation instructions.

## Usage Guide

### Basic Usage

1. Launch the application:
   ```bash
   aretomo3-gui
   ```

2. Set the AreTomo3 executable path in the settings.
3. Load a tilt series using the "Load Tilt Series" button.
4. Configure processing parameters.
5. Click "Process" to start the reconstruction.

### Batch Processing

1. Switch to the "Batch" tab.
2. Add multiple tilt series to the queue.
3. Configure batch processing parameters.
4. Start batch processing.

### Resource Monitoring

The application includes real-time monitoring of:
- CPU usage
- Memory usage
- GPU usage
- Disk space

### Error Handling

- All errors are logged to `logs/aretomo3_gui_[date].log`
- Critical errors display a dialog with error details
- Resource warnings are shown when thresholds are exceeded
