# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env
.venv
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs and databases
*.log
*.sqlite3
logs/

# Test coverage
.coverage
htmlcov/
.tox/
.coverage.*
coverage.xml
*.cover

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# AT3GUI specific ignores
# =====================

# Large data files (should be stored elsewhere)
*.mrc
*.eer
*.tif
*.tiff
*.dm4
*.gain

# Temporary files
temp/
tmp/
*.tmp
*.temp
*_temp.py
test_*.py
verify_*.py

# User data and settings
user_data/
settings/
config/user_*

# Application cache
.aretomo3_gui/
.cache/
.matplotlib/

# Development artifacts
debug_*.py
profile_*.py
*.prof
*.profile

# Exception: Keep test data and example files
!tests/Test_Input_*/
!tests/sample_data/
!rec_TS_85.mrc
