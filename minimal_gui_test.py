#!/usr/bin/env python3
"""
Minimal GUI Test - Just show a simple window
"""

import sys
import os

def main():
    print("🧪 Minimal GUI Test")
    print("=" * 30)
    
    try:
        # Import PyQt6
        from PyQt6.QtWidgets import QApplication, Q<PERSON>ainWindow, QLabel
        from PyQt6.QtCore import Qt
        
        print("✅ PyQt6 imported successfully")
        
        # Create application
        app = QApplication(sys.argv)
        print("✅ QApplication created")
        
        # Create simple window
        window = QMainWindow()
        window.setWindowTitle("AT3GUI Test Window")
        window.setGeometry(200, 200, 300, 200)
        
        # Add simple label
        label = QLabel("Hello! AT3GUI is working!")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        window.setCentralWidget(label)
        
        print("✅ Window created")
        
        # Show window
        window.show()
        print("✅ Window shown - you should see it now!")
        
        # Keep window open for 5 seconds, then close
        from PyQt6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(5000)  # 5 seconds
        
        print("⏰ Window will close automatically in 5 seconds...")
        
        # Run event loop
        result = app.exec()
        print(f"✅ Application finished with code: {result}")
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
