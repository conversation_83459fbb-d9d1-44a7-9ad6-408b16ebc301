# 🚀 AT3GUI Installation & Deployment Guide

## 📋 **Quick Start**

### **System Requirements**
- **Python**: 3.8 or higher (3.9+ recommended)
- **Operating System**: Linux, macOS, or Windows
- **Memory**: 4GB RAM minimum (8GB+ recommended for large tomograms)
- **Storage**: 1GB free space for installation + space for data

### **Dependencies**
- **PyQt6**: GUI framework
- **NumPy**: Numerical computing
- **Matplotlib**: Plotting and visualization
- **mrcfile**: MRC file format support
- **psutil**: System monitoring
- **Pillow**: Image processing
- **h5py**: HDF5 file support
- **tifffile**: TIFF file support

---

## 🔧 **Installation Methods**

### **Method 1: Quick Installation (Recommended)**

```bash
# 1. Clone or download AT3GUI
cd /path/to/your/projects
git clone <repository-url> AT3GUI
# OR extract from archive

# 2. Navigate to AT3GUI directory
cd AT3GUI

# 3. Create virtual environment
python3 -m venv venv

# 4. Activate virtual environment
source venv/bin/activate  # Linux/macOS
# OR
venv\Scripts\activate     # Windows

# 5. Install dependencies
pip install -r requirements.txt

# 6. Launch AT3GUI
python -m aretomo3_gui.main
```

### **Method 2: Development Installation**

```bash
# Follow steps 1-4 from Method 1, then:

# 5. Install development dependencies
pip install -r requirements-dev.txt

# 6. Install in editable mode
pip install -e .

# 7. Launch AT3GUI
aretomo3-gui
# OR
python -m aretomo3_gui.main
```

### **Method 3: Using pyproject.toml (Modern)**

```bash
# Follow steps 1-4 from Method 1, then:

# 5. Install using pip with pyproject.toml
pip install .

# 6. Launch AT3GUI
aretomo3-gui
```

---

## 🐍 **Python Environment Setup**

### **Using Conda (Alternative)**

```bash
# 1. Create conda environment
conda create -n at3gui python=3.9

# 2. Activate environment
conda activate at3gui

# 3. Install dependencies
pip install -r requirements.txt

# 4. Launch AT3GUI
python -m aretomo3_gui.main
```

### **Using Poetry (Alternative)**

```bash
# 1. Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# 2. Install dependencies
poetry install

# 3. Launch AT3GUI
poetry run python -m aretomo3_gui.main
```

---

## 🚀 **Launching AT3GUI**

### **Command Line Options**

```bash
# Basic launch
python -m aretomo3_gui.main

# With specific data directory
python -m aretomo3_gui.main --data-dir /path/to/data

# With debug logging
python -m aretomo3_gui.main --debug

# Show help
python -m aretomo3_gui.main --help
```

### **Desktop Launcher (Linux)**

Create `~/.local/share/applications/at3gui.desktop`:

```ini
[Desktop Entry]
Name=AT3GUI
Comment=AreTomo3 Graphical User Interface
Exec=/path/to/AT3GUI/venv/bin/python -m aretomo3_gui.main
Icon=/path/to/AT3GUI/icon.png
Terminal=false
Type=Application
Categories=Science;Education;
```

### **Batch Script (Windows)**

Create `launch_at3gui.bat`:

```batch
@echo off
cd /d "C:\path\to\AT3GUI"
call venv\Scripts\activate
python -m aretomo3_gui.main
pause
```

---

## 🔍 **Troubleshooting**

### **Common Issues**

#### **1. PyQt6 Installation Issues**
```bash
# If PyQt6 fails to install:
pip install --upgrade pip setuptools wheel
pip install PyQt6 --no-cache-dir

# Alternative: Use conda
conda install pyqt
```

#### **2. mrcfile Import Errors**
```bash
# Install specific version
pip install mrcfile==1.5.0

# Or upgrade
pip install --upgrade mrcfile
```

#### **3. Display Issues (Linux)**
```bash
# Set display variable
export DISPLAY=:0

# Install X11 forwarding (if using SSH)
ssh -X username@hostname
```

#### **4. Permission Errors**
```bash
# Fix permissions
chmod +x launch_at3gui.py
chmod -R 755 aretomo3_gui/
```

### **Dependency Conflicts**

```bash
# Create fresh environment
rm -rf venv/
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### **Memory Issues with Large Files**

```bash
# Increase Python memory limit
export PYTHONHASHSEED=0
ulimit -v 8388608  # 8GB virtual memory limit
```

---

## 📊 **Verification**

### **Test Installation**

```bash
# Run import tests
python test_comprehensive_imports.py

# Run basic functionality tests
python test_application_startup.py

# Test MRC file handling
python test_mrc_viewer.py
```

### **Expected Output**
```
🔬 AT3GUI Application Startup Testing
============================================================
✅ Main Module Import............ PASS
✅ GUI Class Creation............ PASS
✅ Theme System.................. PASS
✅ Configuration System.......... PASS
✅ Logging System................ PASS
✅ File Operations............... PASS
✅ MRC File Support.............. PASS

🎯 Overall Success Rate: 7/7 (100%)
🎉 EXCELLENT! Application startup is perfect!
```

---

## 🔧 **Configuration**

### **First Run Setup**

1. **Launch AT3GUI**
2. **Configure data directories** in Settings
3. **Set up microscope profiles** for your instrument
4. **Test with sample data** (rec_TS_85.mrc included)
5. **Customize themes** and preferences

### **Configuration Files**

- `~/.aretomo3/config.json` - Main configuration
- `~/.aretomo3/profiles.json` - Microscope profiles
- `~/.aretomo3/presets.json` - Processing presets

---

## 📈 **Performance Optimization**

### **For Large Datasets**

```bash
# Increase memory limits
export OMP_NUM_THREADS=4
export MKL_NUM_THREADS=4

# Use SSD for temporary files
export TMPDIR=/path/to/fast/storage
```

### **GPU Acceleration**

```bash
# Install CUDA support (if available)
pip install cupy-cuda11x  # For CUDA 11.x
pip install cupy-cuda12x  # For CUDA 12.x
```

---

## 🆘 **Support**

### **Getting Help**

1. **Check logs**: `logs/aretomo3_gui_YYYYMMDD.log`
2. **Run diagnostics**: `python -m aretomo3_gui.main --diagnose`
3. **Check system requirements**: Ensure Python 3.8+, sufficient RAM
4. **Update dependencies**: `pip install --upgrade -r requirements.txt`

### **Reporting Issues**

Include in bug reports:
- Operating system and version
- Python version (`python --version`)
- AT3GUI version
- Error messages and log files
- Steps to reproduce

---

## ✅ **Success Checklist**

- [ ] Python 3.8+ installed
- [ ] Virtual environment created and activated
- [ ] Dependencies installed successfully
- [ ] AT3GUI launches without errors
- [ ] Can load and view MRC files
- [ ] All tabs and features accessible
- [ ] No critical error messages in logs

**🎉 Congratulations! AT3GUI is ready for use!**
