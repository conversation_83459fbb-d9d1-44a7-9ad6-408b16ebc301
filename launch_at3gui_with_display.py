#!/usr/bin/env python3
"""
AT3GUI Display-Aware Launcher
Handles various display scenarios for external users.
"""

import sys
import os
import subprocess
from pathlib import Path

def print_header(text):
    print(f"\n🚀 {text}")
    print("=" * (len(text) + 4))

def print_info(text):
    print(f"ℹ️  {text}")

def print_success(text):
    print(f"✅ {text}")

def print_warning(text):
    print(f"⚠️  {text}")

def print_error(text):
    print(f"❌ {text}")

def check_display_environment():
    """Check and diagnose display environment."""
    print_header("Display Environment Check")
    
    display = os.environ.get('DISPLAY')
    xdg_session = os.environ.get('XDG_SESSION_TYPE')
    wayland_display = os.environ.get('WAYLAND_DISPLAY')
    
    print_info(f"DISPLAY: {display or 'Not set'}")
    print_info(f"XDG_SESSION_TYPE: {xdg_session or 'Not set'}")
    print_info(f"WAYLAND_DISPLAY: {wayland_display or 'Not set'}")
    
    # Determine best display method
    if display:
        print_success("X11 display detected")
        return "x11"
    elif wayland_display:
        print_success("Wayland display detected")
        return "wayland"
    elif xdg_session == "wayland":
        print_warning("Wayland session but no WAYLAND_DISPLAY")
        return "wayland"
    else:
        print_warning("No display environment detected")
        return "headless"

def setup_display_environment(display_type):
    """Set up the appropriate display environment."""
    print_header("Setting Up Display Environment")
    
    if display_type == "x11":
        os.environ['QT_QPA_PLATFORM'] = 'xcb'
        print_success("Configured for X11 (xcb)")
        
    elif display_type == "wayland":
        os.environ['QT_QPA_PLATFORM'] = 'wayland'
        print_success("Configured for Wayland")
        
    else:  # headless
        print_warning("No display available - using virtual display")
        
        # Try to use xvfb if available
        if subprocess.run(['which', 'xvfb-run'], capture_output=True).returncode == 0:
            print_info("xvfb-run available - will use virtual display")
            return "xvfb"
        else:
            print_warning("xvfb not available - using offscreen rendering")
            os.environ['QT_QPA_PLATFORM'] = 'offscreen'
            return "offscreen"
    
    return display_type

def launch_with_xvfb():
    """Launch AT3GUI with xvfb virtual display."""
    print_header("Launching with Virtual Display (xvfb)")
    
    cmd = [
        'xvfb-run', '-a', '--server-args=-screen 0 1920x1080x24',
        sys.executable, 'launch_at3gui.py'
    ]
    
    print_info("Starting virtual X server...")
    print_info("Command: " + ' '.join(cmd))
    
    try:
        result = subprocess.run(cmd, cwd=Path.cwd())
        return result.returncode
    except Exception as e:
        print_error(f"Failed to launch with xvfb: {e}")
        return 1

def launch_direct():
    """Launch AT3GUI directly."""
    print_header("Launching AT3GUI")
    
    try:
        # Import and run
        from aretomo3_gui.main import main as app_main
        print_success("AT3GUI modules imported")
        print_info("Starting GUI application...")
        
        return app_main()
        
    except Exception as e:
        print_error(f"Failed to launch AT3GUI: {e}")
        import traceback
        traceback.print_exc()
        return 1

def show_usage_instructions():
    """Show usage instructions for different scenarios."""
    print_header("AT3GUI Launch Instructions")
    
    print("\n📋 For different scenarios:")
    print("\n1. 🖥️  Local Desktop (with display):")
    print("   python launch_at3gui_with_display.py")
    
    print("\n2. 🌐 SSH Connection:")
    print("   ssh -X username@hostname")
    print("   python launch_at3gui_with_display.py")
    
    print("\n3. 🔧 Headless Server:")
    print("   # Install xvfb first:")
    print("   sudo apt-get install xvfb  # Ubuntu/Debian")
    print("   sudo dnf install xorg-x11-server-Xvfb  # CentOS/RHEL")
    print("   python launch_at3gui_with_display.py")
    
    print("\n4. 🐳 Docker/Container:")
    print("   export QT_QPA_PLATFORM=offscreen")
    print("   python launch_at3gui_with_display.py")
    
    print("\n5. 📱 Remote Desktop (VNC/RDP):")
    print("   python launch_at3gui_with_display.py")

def main():
    """Main launcher function."""
    print("🚀 AT3GUI Display-Aware Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("aretomo3_gui/main.py").exists():
        print_error("AT3GUI not found in current directory")
        print_info("Please run this script from the AT3GUI root directory")
        return 1
    
    # Check virtual environment
    if not os.environ.get('VIRTUAL_ENV'):
        print_warning("Virtual environment not detected")
        print_info("Recommended: source venv/bin/activate")
        print_info("Continuing anyway...")
    
    # Check display environment
    display_type = check_display_environment()
    
    # Set up display
    launch_method = setup_display_environment(display_type)
    
    # Launch based on method
    if launch_method == "xvfb":
        return launch_with_xvfb()
    elif launch_method == "offscreen":
        print_warning("Using offscreen rendering - no GUI window will appear")
        print_info("AT3GUI will run but you won't see the interface")
        return launch_direct()
    else:
        return launch_direct()

if __name__ == "__main__":
    try:
        result = main()
        if result != 0:
            print("\n" + "=" * 50)
            show_usage_instructions()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n⚠️  Launch cancelled by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
