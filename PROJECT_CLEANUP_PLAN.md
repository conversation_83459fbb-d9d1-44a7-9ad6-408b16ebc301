# AT3GUI Project Cleanup & Reorganization Plan

## 🎯 Current Issues Identified

### **Major Problems:**
1. **Duplicate directory structures** (`src/` and `aretomo3_gui/` both exist)
2. **Multiple entry points** (`main.py` in root and `aretomo3_gui/main.py`)
3. **Scattered test files** (both in root and `tests/` directory)
4. **Duplicate documentation** (multiple README files)
5. **Temporary/development files** cluttering the root
6. **Inconsistent module organization**
7. **Large virtual environment** in project directory

### **Files to Remove:**
- **Duplicate/temporary files**: `SPINBOX_ARROW_FIX*.py`, `test_*.py` in root
- **Duplicate documentation**: `README_ENHANCED.md`, `FINAL_STATUS_REPORT.md`
- **Development artifacts**: `verify_arrow_fix.py`, `test_*.py` files
- **Duplicate source structure**: Either `src/` or keep `aretomo3_gui/`
- **Virtual environment**: `venv/` (should be in `.gitignore`)
- **Cache files**: `__pycache__/` directories
- **Duplicate CLI**: `cli.py` in root

## 🏗️ Target Professional Structure

```
AT3GUI/
├── README.md                          # Main documentation
├── LICENSE                           # License file
├── CHANGELOG.md                      # Version history
├── pyproject.toml                    # Modern Python packaging
├── requirements.txt                  # Production dependencies
├── requirements-dev.txt              # Development dependencies
├── .gitignore                        # Git ignore rules
├── .github/                          # GitHub workflows
│   └── workflows/
│       ├── ci.yml                    # Continuous integration
│       └── release.yml               # Release automation
├── docs/                             # Documentation
│   ├── index.md                      # Main documentation
│   ├── user_guide.md                 # User guide
│   ├── api/                          # API documentation
│   └── images/                       # Screenshots/diagrams
├── src/                              # Source code (modern structure)
│   └── aretomo3_gui/
│       ├── __init__.py
│       ├── main.py                   # Application entry point
│       ├── core/                     # Core business logic
│       │   ├── __init__.py
│       │   ├── config/               # Configuration management
│       │   ├── automation/           # Workflow automation
│       │   ├── error_handling.py     # Error handling
│       │   ├── logging_config.py     # Logging configuration
│       │   ├── system_monitor.py     # System monitoring
│       │   └── thread_manager.py     # Thread management
│       ├── gui/                      # User interface
│       │   ├── __init__.py
│       │   ├── main_window.py        # Main application window
│       │   ├── theme_manager.py      # Theme management
│       │   ├── viewers/              # Data viewers
│       │   │   ├── __init__.py
│       │   │   ├── mrc_viewer.py     # MRC file viewer
│       │   │   └── analysis_viewer.py # Analysis viewer
│       │   └── widgets/              # Reusable widgets
│       │       ├── __init__.py
│       │       ├── enhanced_spinbox.py
│       │       └── advanced_settings_tab.py
│       ├── utils/                    # Utility functions
│       │   ├── __init__.py
│       │   ├── export_functions.py   # Export utilities
│       │   ├── mdoc_parser.py        # MDOC file parsing
│       │   └── file_utils.py         # File operations
│       └── tools/                    # Specialized tools
│           ├── __init__.py
│           └── kmeans_integration.py
├── tests/                            # Test suite
│   ├── __init__.py
│   ├── conftest.py                   # Test configuration
│   ├── unit/                         # Unit tests
│   │   ├── test_core/
│   │   ├── test_gui/
│   │   └── test_utils/
│   ├── integration/                  # Integration tests
│   └── data/                         # Test data
│       ├── sample_data/              # Small test files
│       └── Test_Input_1/             # Existing test data
├── scripts/                          # Utility scripts
│   ├── install.sh                    # Installation script
│   ├── run_tests.sh                  # Test runner
│   └── build.sh                      # Build script
└── examples/                         # Usage examples
    ├── basic_usage.py
    └── batch_processing.py
```

## 🧹 Cleanup Actions

### **Phase 1: Remove Unwanted Files**
1. Delete duplicate/temporary files
2. Remove development artifacts
3. Clean up cache directories
4. Remove virtual environment from repo

### **Phase 2: Reorganize Structure**
1. Consolidate to single source structure (`src/aretomo3_gui/`)
2. Move all source files to proper locations
3. Organize tests properly
4. Create proper documentation structure

### **Phase 3: Update Configuration**
1. Create modern `pyproject.toml`
2. Update `.gitignore`
3. Fix import paths
4. Update entry points

### **Phase 4: Documentation**
1. Consolidate documentation
2. Create proper API docs
3. Update README with new structure
4. Add development guide

## 📋 Files to Keep vs Remove

### **✅ Keep:**
- `aretomo3_gui/` (main source)
- `tests/Test_Input_*` (test data)
- `tests/conftest.py`, `tests/test_*.py` (proper tests)
- `README.md`, `LICENSE`, `CHANGELOG.md`
- `requirements*.txt`
- `setup.py`, `setup.cfg` (for now)
- `docs/user_guide.md`

### **❌ Remove:**
- `src/` (duplicate structure)
- `cli.py` (duplicate)
- `main.py` (duplicate, use aretomo3_gui/main.py)
- `test_*.py` (root level test files)
- `SPINBOX_ARROW_FIX*.py`
- `verify_arrow_fix.py`
- `README_ENHANCED.md`
- `FINAL_STATUS_REPORT.md`
- `venv/` (virtual environment)
- `__pycache__/` (cache directories)
- `logs/` (should be in user data directory)

### **🔄 Reorganize:**
- Move `aretomo3_gui/` to `src/aretomo3_gui/`
- Consolidate duplicate modules
- Organize tests by category
- Create proper scripts directory

## 🎯 Benefits of Cleanup

1. **Professional Structure**: Modern Python project layout
2. **Clear Organization**: Logical separation of concerns
3. **Easier Maintenance**: No duplicate code or files
4. **Better Testing**: Organized test structure
5. **Improved Documentation**: Centralized and comprehensive
6. **Simplified Deployment**: Clean build process
7. **Version Control**: Proper .gitignore and structure

## 📝 Next Steps

1. **Backup current state**
2. **Execute cleanup plan**
3. **Test functionality**
4. **Update documentation**
5. **Verify all imports work**
6. **Run test suite**
