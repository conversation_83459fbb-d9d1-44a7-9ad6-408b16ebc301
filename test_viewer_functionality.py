#!/usr/bin/env python3
"""
Comprehensive test script for AT3GUI viewer functionality.
Tests the enhanced viewer tab with the 3D tomogram file.
"""

import sys
import os
import time
import mrcfile
import numpy as np
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/mnt/HDD/ak_devel/AT3Gui')

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from aretomo3_gui.gui.main_window import AreTomoG<PERSON>

def test_mrc_file_info(file_path):
    """Test and display MRC file information."""
    print(f"\n{'='*60}")
    print(f"TESTING MRC FILE: {file_path}")
    print(f"{'='*60}")
    
    try:
        with mrcfile.open(file_path, permissive=True) as mrc:
            print(f"✓ File opened successfully")
            print(f"  Data shape: {mrc.data.shape}")
            print(f"  Data type: {mrc.data.dtype}")
            print(f"  Voxel size: {mrc.voxel_size.x:.3f} x {mrc.voxel_size.y:.3f} x {mrc.voxel_size.z:.3f}")
            print(f"  Min value: {mrc.data.min():.3f}")
            print(f"  Max value: {mrc.data.max():.3f}")
            print(f"  Mean value: {mrc.data.mean():.3f}")
            print(f"  Standard deviation: {mrc.data.std():.3f}")
            
            # Check if it's a 3D volume
            if len(mrc.data.shape) == 3:
                print(f"  ✓ 3D tomogram detected with {mrc.data.shape[0]} slices")
                print(f"  Slice dimensions: {mrc.data.shape[1]} x {mrc.data.shape[2]}")
            else:
                print(f"  ⚠ Not a 3D volume (shape: {mrc.data.shape})")
                
            return True
    except Exception as e:
        print(f"✗ Error reading MRC file: {e}")
        return False

def test_viewer_functionality():
    """Test the enhanced viewer functionality."""
    print(f"\n{'='*60}")
    print("TESTING ENHANCED VIEWER FUNCTIONALITY")
    print(f"{'='*60}")
    
    app = QApplication(sys.argv)
    
    try:
        # Create the main window
        print("Creating AT3GUI main window...")
        main_window = AreTomoGUI()
        main_window.show()
        
        # Test file path
        test_file = "/mnt/HDD/ak_devel/AT3Gui/rec_TS_85.mrc"
        
        if not os.path.exists(test_file):
            print(f"✗ Test file not found: {test_file}")
            return False
            
        print(f"✓ Test file found: {test_file}")
        
        # Test viewer tab functionality
        print("\nTesting viewer tab features:")
        
        # 1. Test file browser functionality
        print("  1. Testing file browser...")
        if hasattr(main_window, 'current_dir_edit'):
            main_window.current_dir_edit.setText(os.path.dirname(test_file))
            main_window._update_file_list()
            print("     ✓ File browser directory set")
        
        # 2. Test MRC file loading
        print("  2. Testing MRC file loading...")
        if hasattr(main_window, 'mrc_viewer'):
            success = main_window.mrc_viewer.load_mrc(test_file)
            if success:
                print("     ✓ MRC file loaded successfully")
                
                # Test metadata update
                main_window._update_metadata(test_file)
                print("     ✓ Metadata updated")
            else:
                print("     ✗ Failed to load MRC file")
        
        # 3. Test viewer controls
        print("  3. Testing viewer controls...")
        
        # Test slice navigation
        if hasattr(main_window, 'slice_slider') and hasattr(main_window, 'slice_spinbox'):
            main_window.slice_slider.setValue(10)
            main_window.slice_spinbox.setValue(10)
            print("     ✓ Slice navigation controls")
        
        # Test contrast controls
        if hasattr(main_window, 'contrast_min') and hasattr(main_window, 'contrast_max'):
            main_window.contrast_min.setValue(20)
            main_window.contrast_max.setValue(80)
            main_window._update_contrast()
            print("     ✓ Contrast controls")
        
        # Test colormap selection
        if hasattr(main_window, 'viewer_colormap'):
            main_window.viewer_colormap.setCurrentText("viridis")
            main_window._update_viewer_colormap("viridis")
            print("     ✓ Colormap selection")
        
        # Test view mode changes
        if hasattr(main_window, 'view_mode'):
            main_window.view_mode.setCurrentText("2D Slice View")
            main_window._change_view_mode("2D Slice View")
            print("     ✓ View mode selection")
        
        # Test orientation changes
        if hasattr(main_window, 'view_orientation'):
            main_window.view_orientation.setCurrentText("XY (Top)")
            main_window._change_orientation("XY (Top)")
            print("     ✓ View orientation")
        
        # 4. Test measurement tools
        print("  4. Testing measurement tools...")
        main_window._toggle_distance_tool(True)
        main_window._toggle_angle_tool(True)
        main_window._toggle_area_tool(True)
        print("     ✓ Measurement tools activated")
        
        # 5. Test zoom controls
        print("  5. Testing zoom controls...")
        main_window._zoom_in()
        main_window._zoom_out()
        main_window._zoom_fit()
        print("     ✓ Zoom controls")
        
        # 6. Test display options
        print("  6. Testing display options...")
        if hasattr(main_window, 'show_scale_bar'):
            main_window.show_scale_bar.setChecked(True)
            main_window._toggle_scale_bar(True)
        if hasattr(main_window, 'show_grid'):
            main_window.show_grid.setChecked(True)
            main_window._toggle_grid(True)
        print("     ✓ Display options")
        
        # 7. Test histogram functionality
        print("  7. Testing histogram...")
        if hasattr(main_window, 'hist_bins'):
            main_window.hist_bins.setValue(50)
            main_window._update_histogram()
        print("     ✓ Histogram controls")
        
        # 8. Test export functionality
        print("  8. Testing export functions...")
        main_window._save_current_view()
        main_window._export_viewer_data()
        print("     ✓ Export functions")
        
        print(f"\n{'='*60}")
        print("✅ ALL VIEWER TESTS COMPLETED SUCCESSFULLY!")
        print(f"{'='*60}")
        
        # Keep the window open for a moment to see it
        QTimer.singleShot(2000, app.quit)  # Close after 2 seconds
        app.exec()
        
        return True
        
    except Exception as e:
        print(f"✗ Error during viewer testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("AT3GUI Enhanced Viewer Functionality Test")
    print("=" * 60)
    
    # Test 1: Check MRC file
    test_file = "/mnt/HDD/ak_devel/AT3Gui/rec_TS_85.mrc"
    if not test_mrc_file_info(test_file):
        print("❌ MRC file test failed")
        return 1
    
    # Test 2: Test viewer functionality
    if not test_viewer_functionality():
        print("❌ Viewer functionality test failed")
        return 1
    
    print("\n🎉 ALL TESTS PASSED! Enhanced viewer is working correctly!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
