#!/usr/bin/env python3
"""
AT3GUI Test Suite Runner
Comprehensive test runner for all AT3GUI test categories.
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def run_test_file(test_file, category):
    """Run a single test file and return results."""
    print(f"\n🧪 Running {category}: {test_file.name}")
    print("=" * 60)
    
    start_time = time.time()
    try:
        result = subprocess.run(
            [sys.executable, str(test_file)],
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ PASSED ({duration:.1f}s)")
            return True, duration, result.stdout
        else:
            print(f"❌ FAILED ({duration:.1f}s)")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False, duration, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT (>120s)")
        return False, 120, "Test timed out"
    except Exception as e:
        print(f"💥 ERROR: {e}")
        return False, 0, str(e)

def run_validation_tests():
    """Run validation tests (import and basic functionality)."""
    print("\n🔍 VALIDATION TESTS")
    print("=" * 60)
    
    validation_dir = project_root / "tests" / "validation"
    test_files = list(validation_dir.glob("test_*.py"))
    
    results = []
    for test_file in sorted(test_files):
        success, duration, output = run_test_file(test_file, "Validation")
        results.append((test_file.name, success, duration))
    
    return results

def run_unit_tests():
    """Run unit tests (core functionality and GUI components)."""
    print("\n🔧 UNIT TESTS")
    print("=" * 60)
    
    unit_dir = project_root / "tests" / "unit"
    test_files = list(unit_dir.glob("test_*.py"))
    
    results = []
    for test_file in sorted(test_files):
        success, duration, output = run_test_file(test_file, "Unit")
        results.append((test_file.name, success, duration))
    
    return results

def run_integration_tests():
    """Run integration tests (application startup and MRC handling)."""
    print("\n🔗 INTEGRATION TESTS")
    print("=" * 60)
    
    integration_dir = project_root / "tests" / "integration"
    test_files = list(integration_dir.glob("test_*.py"))
    
    results = []
    for test_file in sorted(test_files):
        success, duration, output = run_test_file(test_file, "Integration")
        results.append((test_file.name, success, duration))
    
    return results

def run_pytest_tests():
    """Run existing pytest test suite."""
    print("\n🧪 PYTEST TEST SUITE")
    print("=" * 60)
    
    tests_dir = project_root / "tests"
    pytest_files = [f for f in tests_dir.glob("test_*.py") if f.name not in [
        'test_comprehensive_imports.py', 'test_application_startup.py',
        'test_core_functionality.py', 'test_gui_basic.py', 'test_mrc_viewer.py'
    ]]
    
    if not pytest_files:
        print("ℹ️  No additional pytest files found")
        return []
    
    results = []
    for test_file in sorted(pytest_files):
        success, duration, output = run_test_file(test_file, "Pytest")
        results.append((test_file.name, success, duration))
    
    return results

def print_summary(all_results):
    """Print comprehensive test summary."""
    print("\n" + "=" * 80)
    print("📊 AT3GUI TEST SUITE SUMMARY")
    print("=" * 80)
    
    total_tests = 0
    passed_tests = 0
    total_time = 0
    
    for category, results in all_results.items():
        if not results:
            continue
            
        category_passed = sum(1 for _, success, _ in results if success)
        category_total = len(results)
        category_time = sum(duration for _, _, duration in results)
        
        print(f"\n{category.upper()} TESTS:")
        for test_name, success, duration in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"  {test_name:<40} {status} ({duration:.1f}s)")
        
        print(f"  📊 {category} Summary: {category_passed}/{category_total} passed ({category_time:.1f}s)")
        
        total_tests += category_total
        passed_tests += category_passed
        total_time += category_time
    
    # Overall summary
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n🎯 OVERALL RESULTS:")
    print(f"   Tests Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    print(f"   Total Time: {total_time:.1f} seconds")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT! Test suite passed with flying colors!")
        return 0
    elif success_rate >= 80:
        print("✅ GOOD! Most tests passing, minor issues to address.")
        return 0
    elif success_rate >= 70:
        print("⚠️  ACCEPTABLE! Some tests failing, needs attention.")
        return 1
    else:
        print("❌ CRITICAL! Major test failures, requires immediate fixing.")
        return 1

def main():
    """Run comprehensive AT3GUI test suite."""
    print("🔬 AT3GUI Comprehensive Test Suite")
    print("=" * 80)
    print(f"Project Root: {project_root}")
    print(f"Python Version: {sys.version}")
    print(f"Test Start Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if we're in the right directory
    if not (project_root / "aretomo3_gui" / "main.py").exists():
        print("❌ Error: Please run this script from the AT3GUI root directory")
        return 1
    
    # Run all test categories
    all_results = {}
    
    try:
        all_results["Validation"] = run_validation_tests()
        all_results["Unit"] = run_unit_tests()
        all_results["Integration"] = run_integration_tests()
        all_results["Pytest"] = run_pytest_tests()
        
    except KeyboardInterrupt:
        print("\n⚠️  Test suite interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        return 1
    
    # Print comprehensive summary
    return print_summary(all_results)

if __name__ == "__main__":
    sys.exit(main())
