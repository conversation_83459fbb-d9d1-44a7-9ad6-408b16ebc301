#!/bin/bash
# AT3GUI Activation and Launch Script
# Automatically activates virtual environment and launches AT3GUI

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 AT3GUI Activation & Launch Script"
echo "====================================="
echo ""

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_error "Virtual environment not found!"
    print_info "Please run the setup script first:"
    echo "  ./setup_at3gui.sh"
    exit 1
fi

# Check if AT3GUI is installed
if [ ! -f "aretomo3_gui/main.py" ]; then
    print_error "AT3GUI source code not found!"
    print_info "Please ensure you're in the AT3GUI directory"
    exit 1
fi

# Activate virtual environment
print_info "Activating virtual environment..."
source venv/bin/activate

# Verify activation
if [ "$VIRTUAL_ENV" != "$(pwd)/venv" ]; then
    print_error "Failed to activate virtual environment"
    exit 1
fi

print_status "Virtual environment activated: $VIRTUAL_ENV"

# Check if dependencies are installed
print_info "Checking dependencies..."
if ! python -c "import PyQt6" 2>/dev/null; then
    print_error "PyQt6 not found in virtual environment"
    print_info "Installing dependencies..."
    pip install -r requirements.txt
fi

print_status "Dependencies verified"

# Launch AT3GUI
print_info "Launching AT3GUI..."
echo ""

# Try different launch methods
if [ -f "launch_at3gui.py" ]; then
    print_info "Using professional launcher..."
    python launch_at3gui.py
elif [ -f "aretomo3_gui/main.py" ]; then
    print_info "Using direct module launch..."
    python -m aretomo3_gui.main
else
    print_error "No launcher found!"
    exit 1
fi
