"""Grid view for tilt series thumbnails with preview capabilities."""
from typing import Dict, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QGridLayout, QLabel, QScrollArea, 
    QVBoxLayout, QFrame, QPushButton, QMenu
)
from PyQt6.QtCore import Qt, pyqtSignal, QSize
from PyQt6.QtGui import QImage, QPixmap, QDragEnterEvent, QDropEvent

class ThumbnailWidget(QFrame):
    """Widget for displaying a single thumbnail with metadata."""
    clicked = pyqtSignal(str)  # Emits file path when clicked
    
    def __init__(self, file_path: str, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.setFrameStyle(QFrame.Shape.Panel | QFrame.Shadow.Raised)
        self.setLineWidth(2)
        self.setMinimumSize(200, 200)
        
        layout = QVBoxLayout(self)
        
        # Thumbnail image
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.image_label)
        
        # Metadata label
        self.meta_label = QLabel()
        self.meta_label.setWordWrap(True)
        layout.addWidget(self.meta_label)
        
        self.setLayout(layout)
        
    def mousePressEvent(self, event):
        super().mousePressEvent(event)
        self.clicked.emit(self.file_path)
        
    def set_thumbnail(self, pixmap: QPixmap):
        """Set the thumbnail image, scaling it appropriately."""
        scaled = pixmap.scaled(180, 180, Qt.AspectRatioMode.KeepAspectRatio)
        self.image_label.setPixmap(scaled)
        
    def set_metadata(self, metadata: Dict[str, str]):
        """Update metadata display."""
        text = "\\n".join(f"{k}: {v}" for k, v in metadata.items())
        self.meta_label.setText(text)

class PreviewGridView(QWidget):
    """Grid view for displaying tilt series thumbnails with preview capability."""
    
    file_selected = pyqtSignal(str)  # Emits selected file path
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        
        # Create scroll area
        scroll = QScrollArea(self)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)
        
        # Container widget for grid
        container = QWidget()
        self.grid_layout = QGridLayout(container)
        scroll.setWidget(container)
        
        # Enable drag and drop
        self.setAcceptDrops(True)
        
    def add_thumbnail(self, file_path: str, pixmap: QPixmap, metadata: Dict[str, str]):
        """Add a new thumbnail to the grid."""
        # Calculate grid position
        count = self.grid_layout.count()
        row = count // 4
        col = count % 4
        
        # Create thumbnail widget
        thumb = ThumbnailWidget(file_path)
        thumb.set_thumbnail(pixmap)
        thumb.set_metadata(metadata)
        thumb.clicked.connect(self.file_selected.emit)
        
        self.grid_layout.addWidget(thumb, row, col)
        
    def clear(self):
        """Remove all thumbnails."""
        while self.grid_layout.count():
            item = self.grid_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
                
    def dragEnterEvent(self, event: QDragEnterEvent):
        """Handle drag enter events for files."""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            
    def dropEvent(self, event: QDropEvent):
        """Handle file drop events."""
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            # Emit signal for parent to handle file
            self.file_selected.emit(file_path)
