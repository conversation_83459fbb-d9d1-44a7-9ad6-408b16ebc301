from PyQt6.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QSpinBox, QGroupBox, QFormLayout

class AdvancedSettingsTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        # Main layout for the tab
        layout = QVBoxLayout()

        # Motion Correction Settings
        motion_correction_group = QGroupBox("Motion Correction Settings")
        motion_correction_layout = QFormLayout()

        self.frame_grouping_spinbox = QSpinBox()
        self.frame_grouping_spinbox.setRange(1, 100)
        self.frame_grouping_spinbox.setValue(10)
        motion_correction_layout.addRow("Frame Grouping:", self.frame_grouping_spinbox)

        self.patch_x_spinbox = QSpinBox()
        self.patch_x_spinbox.setRange(1, 10)
        self.patch_x_spinbox.setValue(1)
        motion_correction_layout.addRow("Patches (X):", self.patch_x_spinbox)

        self.patch_y_spinbox = QSpinBox()
        self.patch_y_spinbox.setRange(1, 10)
        self.patch_y_spinbox.setValue(1)
        motion_correction_layout.addRow("Patches (Y):", self.patch_y_spinbox)

        motion_correction_group.setLayout(motion_correction_layout)
        layout.addWidget(motion_correction_group)

        # Reconstruction Settings
        reconstruction_group = QGroupBox("Reconstruction Settings")
        reconstruction_layout = QFormLayout()

        self.tilt_axis_spinbox = QSpinBox()
        self.tilt_axis_spinbox.setRange(-90, 90)
        self.tilt_axis_spinbox.setValue(0)
        reconstruction_layout.addRow("Tilt Axis (degrees):", self.tilt_axis_spinbox)

        self.volume_height_spinbox = QSpinBox()
        self.volume_height_spinbox.setRange(1, 1000)
        self.volume_height_spinbox.setValue(256)
        reconstruction_layout.addRow("Volume Height (Z):", self.volume_height_spinbox)

        reconstruction_group.setLayout(reconstruction_layout)
        layout.addWidget(reconstruction_group)

        # Set the main layout
        self.setLayout(layout)

    def get_motion_correction_settings(self):
        return {
            "frame_grouping": self.frame_grouping_spinbox.value(),
            "patch_x": self.patch_x_spinbox.value(),
            "patch_y": self.patch_y_spinbox.value()
        }

    def get_reconstruction_settings(self):
        return {
            "tilt_axis": self.tilt_axis_spinbox.value(),
            "volume_height": self.volume_height_spinbox.value()
        }
