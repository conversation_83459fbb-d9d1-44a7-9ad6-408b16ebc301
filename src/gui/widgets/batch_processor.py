"""Enhanced batch processing with templates and scheduling."""
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
from pathlib import Path
from PyQt6.QtCore import Qt, pyqtSignal, QObject, QTime
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QPushButton, QComboBox, QSpinBox, QLabel,
    QProgressBar, QTableWidget, QTableWidgetItem,
    QCheckBox, QTimeEdit, QMessageBox, QInputDialog,
    QGroupBox, QTextEdit
)

@dataclass
class BatchTemplate:
    """Template for batch processing parameters."""
    name: str
    description: str
    parameters: Dict[str, any]
    
@dataclass
class BatchJob:
    """Represents a single job in the batch queue."""
    id: str
    series_name: str
    priority: int
    status: str
    estimated_time: int  # in seconds
    scheduled_time: Optional[datetime] = None

class BatchProcessor(QObject):
    """Manages batch processing of tilt series."""
    
    progress_updated = pyqtSignal(str, int)  # job_id, progress percentage
    job_completed = pyqtSignal(str, bool, str)  # job_id, success, message
    time_updated = pyqtSignal(str, int)  # job_id, remaining seconds
    
    def __init__(self):
        super().__init__()
        self.jobs: Dict[str, BatchJob] = {}
        self.templates: Dict[str, BatchTemplate] = {}
        self.active_jobs: List[str] = []
        self.max_parallel = 2
        
    def add_job(self, job: BatchJob):
        """Add a job to the queue."""
        self.jobs[job.id] = job
        
    def start_processing(self):
        """Start processing the job queue."""
        while len(self.active_jobs) < self.max_parallel:
            next_job = self._get_next_job()
            if not next_job:
                break
            self._start_job(next_job)
            
    def _get_next_job(self) -> Optional[BatchJob]:
        """Get the next job to process based on priority and schedule."""
        now = datetime.now()
        available_jobs = [
            job for job in self.jobs.values()
            if job.id not in self.active_jobs
            and job.status == "queued"
            and (not job.scheduled_time or job.scheduled_time <= now)
        ]
        
        if not available_jobs:
            return None
            
        # Sort by priority (higher number = higher priority)
        return sorted(available_jobs, key=lambda x: x.priority, reverse=True)[0]
        
    def _start_job(self, job: BatchJob):
        """Start processing a job."""
        self.active_jobs.append(job.id)
        job.status = "processing"
        # Start actual processing...
        
    def save_template(self, template: BatchTemplate):
        """Save a parameter template."""
        self.templates[template.name] = template
        self._save_templates_to_disk()
        
    def load_template(self, name: str) -> Optional[BatchTemplate]:
        """Load a parameter template."""
        return self.templates.get(name)
        
    def _save_templates_to_disk(self):
        """Save templates to disk."""
        template_dir = Path.home() / ".aretomo3" / "templates"
        template_dir.mkdir(parents=True, exist_ok=True)
        
        template_data = {
            name: {
                "description": t.description,
                "parameters": t.parameters
            }
            for name, t in self.templates.items()
        }
        
        with open(template_dir / "templates.json", "w") as f:
            json.dump(template_data, f, indent=2)
            
    def _load_templates_from_disk(self):
        """Load templates from disk."""
        template_file = Path.home() / ".aretomo3" / "templates" / "templates.json"
        if not template_file.exists():
            return
            
        with open(template_file) as f:
            data = json.load(f)
            
        self.templates = {
            name: BatchTemplate(
                name=name,
                description=t["description"],
                parameters=t["parameters"]
            )
            for name, t in data.items()
        }

class BatchProcessingWidget(QWidget):
    """Widget for managing batch processing."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.processor = BatchProcessor()
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI."""
        layout = QVBoxLayout(self)
        
        # Template management
        template_group = QGroupBox("Template Management")
        template_layout = QVBoxLayout()
        
        # Template selection
        template_header = QHBoxLayout()
        self.template_combo = QComboBox()
        self.template_combo.addItems(self.processor.templates.keys())
        template_header.addWidget(QLabel("Template:"))
        template_header.addWidget(self.template_combo, stretch=1)
        
        # Save/load buttons
        btn_layout = QHBoxLayout()
        save_template_btn = QPushButton("Save Template")
        save_template_btn.clicked.connect(self._save_current_template)
        load_template_btn = QPushButton("Load Template")
        load_template_btn.clicked.connect(self._load_template)
        delete_template_btn = QPushButton("Delete Template")
        delete_template_btn.clicked.connect(self._delete_template)
        
        btn_layout.addWidget(save_template_btn)
        btn_layout.addWidget(load_template_btn)
        btn_layout.addWidget(delete_template_btn)
        
        template_layout.addLayout(template_header)
        template_layout.addLayout(btn_layout)
        
        # Template description
        self.template_desc = QTextEdit()
        self.template_desc.setMaximumHeight(60)
        self.template_desc.setPlaceholderText("Template description...")
        template_layout.addWidget(self.template_desc)
        
        template_group.setLayout(template_layout)
        layout.addWidget(template_group)
        
        # Processing parameters
        params_group = QGroupBox("Processing Parameters")
        params_layout = QFormLayout()
        
        # GPU selection
        self.gpu_combo = QComboBox()
        self.gpu_combo.addItems(["Auto", "GPU 0", "GPU 1", "GPU 2", "GPU 3"])
        params_layout.addRow("GPU:", self.gpu_combo)
        
        # Parallel processing
        self.parallel_spin = QSpinBox()
        self.parallel_spin.setRange(1, 4)
        self.parallel_spin.setValue(2)
        self.parallel_spin.valueChanged.connect(self._update_parallel_count)
        params_layout.addRow("Parallel Jobs:", self.parallel_spin)
        
        # Batch size
        self.batch_spin = QSpinBox()
        self.batch_spin.setRange(1, 100)
        self.batch_spin.setValue(10)
        params_layout.addRow("Batch Size:", self.batch_spin)
        
        # Auto-start checkbox
        self.auto_start = QCheckBox("Auto-start when ready")
        params_layout.addRow("", self.auto_start)
        
        # Email notification
        self.email_notify = QCheckBox("Email on completion")
        params_layout.addRow("", self.email_notify)
        
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        # Scheduling
        schedule_group = QGroupBox("Job Scheduling")
        schedule_layout = QFormLayout()
        
        # Priority selector
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["Low", "Normal", "High", "Urgent"])
        self.priority_combo.setCurrentText("Normal")
        schedule_layout.addRow("Priority:", self.priority_combo)
        
        # Start time
        self.start_time = QTimeEdit()
        self.start_time.setTime(QTime.currentTime())
        schedule_layout.addRow("Start Time:", self.start_time)
        
        # Time limit
        self.time_limit = QTimeEdit()
        self.time_limit.setDisplayFormat("HH:mm")
        self.time_limit.setTime(QTime(2, 0))  # Default 2 hours
        schedule_layout.addRow("Time Limit:", self.time_limit)
        
        schedule_group.setLayout(schedule_layout)
        layout.addWidget(schedule_group)
        
        # Job queue
        queue_group = QGroupBox("Processing Queue")
        queue_layout = QVBoxLayout()
        
        self.queue_table = QTableWidget()
        self.queue_table.setColumnCount(7)
        self.queue_table.setHorizontalHeaderLabels([
            "Series", "Priority", "Status", "Progress", 
            "Est. Time", "Scheduled", "Actions"
        ])
        self.queue_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        queue_layout.addWidget(self.queue_table)
        
        # Queue controls
        queue_controls = QHBoxLayout()
        
        start_btn = QPushButton("Start Processing")
        start_btn.clicked.connect(self.start_processing)
        
        stop_btn = QPushButton("Stop")
        stop_btn.clicked.connect(self._stop_processing)
        
        clear_btn = QPushButton("Clear Completed")
        clear_btn.clicked.connect(self._clear_completed)
        
        queue_controls.addWidget(start_btn)
        queue_controls.addWidget(stop_btn)
        queue_controls.addWidget(clear_btn)
        
        queue_layout.addLayout(queue_controls)
        queue_group.setLayout(queue_layout)
        layout.addWidget(queue_group)
        
    def _save_current_template(self):
        """Save current parameters as a template."""
        name, ok = QInputDialog.getText(
            self, "Save Template", 
            "Enter template name:"
        )
        if not ok or not name:
            return
            
        if name in self.processor.templates:
            reply = QMessageBox.question(
                self, "Confirm Overwrite",
                f"Template '{name}' already exists. Overwrite?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.No:
                return
                
        template = BatchTemplate(
            name=name,
            description=self.template_desc.toPlainText(),
            parameters={
                "gpu": self.gpu_combo.currentText(),
                "parallel_jobs": self.parallel_spin.value(),
                "batch_size": self.batch_spin.value(),
                "auto_start": self.auto_start.isChecked(),
                "email_notify": self.email_notify.isChecked(),
                "priority": self.priority_combo.currentText(),
                "time_limit": self.time_limit.time().toString()
            }
        )
        
        self.processor.save_template(template)
        
        # Update template combo if needed
        if name not in self.processor.templates:
            self.template_combo.addItem(name)
            
        QMessageBox.information(
            self, "Success",
            f"Template '{name}' saved successfully."
        )
        
    def _load_template(self):
        """Load selected template."""
        template_name = self.template_combo.currentText()
        template = self.processor.load_template(template_name)
        if not template:
            return
            
        # Update UI with template values
        self.template_desc.setPlainText(template.description)
        
        params = template.parameters
        self.gpu_combo.setCurrentText(params.get("gpu", "Auto"))
        self.parallel_spin.setValue(params.get("parallel_jobs", 2))
        self.batch_spin.setValue(params.get("batch_size", 10))
        self.auto_start.setChecked(params.get("auto_start", False))
        self.email_notify.setChecked(params.get("email_notify", False))
        self.priority_combo.setCurrentText(params.get("priority", "Normal"))
        
        time_limit = params.get("time_limit", "02:00")
        self.time_limit.setTime(QTime.fromString(time_limit))
        
    def _delete_template(self):
        """Delete the currently selected template."""
        template_name = self.template_combo.currentText()
        if not template_name:
            return
            
        reply = QMessageBox.question(
            self, "Confirm Delete",
            f"Are you sure you want to delete template '{template_name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.template_combo.removeItem(
                self.template_combo.findText(template_name)
            )
            if template_name in self.processor.templates:
                del self.processor.templates[template_name]
            self.processor._save_templates_to_disk()
            
    def start_processing(self):
        """Start processing the job queue."""
        # Update processor settings
        self.processor.max_parallel = self.parallel_spin.value()
        
        # Start processing
        self.processor.start_processing()
        
        # Update UI
        self._update_queue_table()
        
    def _stop_processing(self):
        """Stop all active jobs."""
        reply = QMessageBox.question(
            self, "Confirm Stop",
            "Are you sure you want to stop all active jobs?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Implementation of stop logic
            pass
            
    def _clear_completed(self):
        """Remove completed jobs from the queue."""
        completed = []
        for job_id, job in self.processor.jobs.items():
            if job.status == "completed":
                completed.append(job_id)
                
        for job_id in completed:
            del self.processor.jobs[job_id]
            
        self._update_queue_table()
        
    def _update_queue_table(self):
        """Update the queue table display."""
        self.queue_table.setRowCount(len(self.processor.jobs))
        
        for row, (job_id, job) in enumerate(self.processor.jobs.items()):
            # Series name
            self.queue_table.setItem(
                row, 0, 
                QTableWidgetItem(job.series_name)
            )
            
            # Priority
            self.queue_table.setItem(
                row, 1,
                QTableWidgetItem(str(job.priority))
            )
            
            # Status
            self.queue_table.setItem(
                row, 2,
                QTableWidgetItem(job.status)
            )
            
            # Progress
            progress = QProgressBar()
            progress.setRange(0, 100)
            if hasattr(job, 'progress'):
                progress.setValue(job.progress)
            self.queue_table.setCellWidget(row, 3, progress)
            
            # Estimated time
            self.queue_table.setItem(
                row, 4,
                QTableWidgetItem(str(timedelta(seconds=job.estimated_time)))
            )
            
            # Scheduled time
            scheduled = job.scheduled_time.strftime('%H:%M:%S') if job.scheduled_time else "ASAP"
            self.queue_table.setItem(
                row, 5,
                QTableWidgetItem(scheduled)
            )
            
            # Action buttons
            actions = QWidget()
            action_layout = QHBoxLayout(actions)
            action_layout.setContentsMargins(0, 0, 0, 0)
            
            if job.status == "queued":
                start_btn = QPushButton("Start")
                start_btn.clicked.connect(lambda x, j=job_id: self._start_job(j))
                action_layout.addWidget(start_btn)
            
            delete_btn = QPushButton("Remove")
            delete_btn.clicked.connect(lambda x, j=job_id: self._remove_job(j))
            action_layout.addWidget(delete_btn)
            
            self.queue_table.setCellWidget(row, 6, actions)
            
    def _start_job(self, job_id: str):
        """Start a specific job."""
        job = self.processor.jobs.get(job_id)
        if job and job.status == "queued":
            self.processor._start_job(job)
            self._update_queue_table()
    
    def _remove_job(self, job_id: str) -> None:
        """Remove a job from the queue."""
        if job_id in self.processor.jobs:
            del self.processor.jobs[job_id]
            self._update_queue_table()
    
    def _update_parallel_count(self, value: int) -> None:
        """Update the maximum number of parallel jobs."""
        self.processor.max_parallel = value
