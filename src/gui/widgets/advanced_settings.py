"""Advanced settings tab for motion correction and reconstruction."""

import logging
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
    QLabel, QSpinBox, QDoubleSpinBox, QComboBox,
    QScrollArea, QCheckBox, QPushButton, QTabWidget,
    QFormLayout, QGridLayout
)
from PyQt6.QtCore import Qt, pyqtSignal
from ..simple_spinbox import EnhancedSpinBox, EnhancedDoubleSpinBox

logger = logging.getLogger(__name__)

class AdvancedSettingsTab(QWidget):
    """Advanced settings tab combining motion correction and reconstruction settings."""
    
    # Signals for value changes
    settings_changed = pyqtSignal(dict)  # Emits when any setting changes
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface."""
        main_layout = QVBoxLayout(self)
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # Motion Correction Group
        motion_group = self.create_motion_correction_group()
        content_layout.addWidget(motion_group)
        
        # Reconstruction Group
        recon_group = self.create_reconstruction_group()
        content_layout.addWidget(recon_group)
        
        # Set the scroll area widget
        scroll.setWidget(content_widget)
        main_layout.addWidget(scroll)
        
    def create_motion_correction_group(self):
        """Create the motion correction settings group."""
        group = QGroupBox("Motion Correction Settings")
        layout = QGridLayout()
        
        # Frame Grouping
        layout.addWidget(QLabel("Frame Grouping:"), 0, 0)
        self.group_spin = EnhancedSpinBox()
        self.group_spin.setRange(1, 50)
        self.group_spin.setValue(1)
        self.group_spin.setToolTip("Number of frames to group for alignment")
        layout.addWidget(self.group_spin, 0, 1)
        
        # Patch Grid
        layout.addWidget(QLabel("Patches (X, Y):"), 1, 0)
        self.patch_x = EnhancedSpinBox()
        self.patch_x.setRange(1, 20)
        self.patch_x.setValue(5)
        self.patch_y = EnhancedSpinBox()
        self.patch_y.setRange(1, 20)
        self.patch_y.setValue(5)
        patch_layout = QHBoxLayout()
        patch_layout.addWidget(self.patch_x)
        patch_layout.addWidget(self.patch_y)
        layout.addLayout(patch_layout, 1, 1)
        
        # Iteration Control
        layout.addWidget(QLabel("Max Iterations:"), 2, 0)
        self.iter_spin = EnhancedSpinBox()
        self.iter_spin.setRange(1, 100)
        self.iter_spin.setValue(15)
        self.iter_spin.setToolTip("Maximum number of alignment iterations")
        layout.addWidget(self.iter_spin, 2, 1)
        
        # Tolerance
        layout.addWidget(QLabel("Convergence Tolerance:"), 3, 0)
        self.tol_spin = EnhancedDoubleSpinBox()
        self.tol_spin.setRange(0.01, 1.0)
        self.tol_spin.setValue(0.1)
        self.tol_spin.setSingleStep(0.01)
        layout.addWidget(self.tol_spin, 3, 1)
        
        # Reference Frame
        layout.addWidget(QLabel("Reference Frame:"), 4, 0)
        self.ref_spin = EnhancedSpinBox()
        self.ref_spin.setRange(-1, 100)
        self.ref_spin.setValue(-1)
        self.ref_spin.setToolTip("-1 for automatic selection")
        layout.addWidget(self.ref_spin, 4, 1)
        
        group.setLayout(layout)
        return group
        
    def create_reconstruction_group(self):
        """Create the reconstruction settings group."""
        group = QGroupBox("Reconstruction Settings")
        layout = QGridLayout()
        
        # Tilt Axis
        layout.addWidget(QLabel("Tilt Axis:"), 0, 0)
        self.tilt_spin = EnhancedDoubleSpinBox()
        self.tilt_spin.setRange(-360, 360)
        self.tilt_spin.setValue(0)
        layout.addWidget(self.tilt_spin, 0, 1)
        
        # Auto Refinement
        self.auto_refine = QCheckBox("Auto-refine Tilt Axis")
        self.auto_refine.setChecked(True)
        layout.addWidget(self.auto_refine, 1, 0, 1, 2)
        
        # Volume Dimensions
        layout.addWidget(QLabel("Volume Height:"), 2, 0)
        self.vol_height = EnhancedSpinBox()
        self.vol_height.setRange(64, 8192)
        self.vol_height.setValue(512)
        layout.addWidget(self.vol_height, 2, 1)
        
        # Reconstruction Method
        layout.addWidget(QLabel("Method:"), 3, 0)
        self.recon_method = QComboBox()
        self.recon_method.addItems(["SART", "WBP"])
        layout.addWidget(self.recon_method, 3, 1)
        
        # SART Parameters
        self.sart_group = QGroupBox("SART Parameters")
        sart_layout = QFormLayout()
        
        self.sart_iter = EnhancedSpinBox()
        self.sart_iter.setRange(1, 50)
        self.sart_iter.setValue(15)
        sart_layout.addRow("Iterations:", self.sart_iter)
        
        self.sart_relax = EnhancedDoubleSpinBox()
        self.sart_relax.setRange(0.1, 2.0)
        self.sart_relax.setValue(0.3)
        self.sart_relax.setSingleStep(0.1)
        sart_layout.addRow("Relaxation:", self.sart_relax)
        
        self.sart_group.setLayout(sart_layout)
        layout.addWidget(self.sart_group, 4, 0, 1, 2)
        
        # CTF Correction
        self.ctf_group = QGroupBox("CTF Correction")
        ctf_layout = QFormLayout()
        
        self.amp_contrast = EnhancedDoubleSpinBox()
        self.amp_contrast.setRange(0.0, 1.0)
        self.amp_contrast.setValue(0.07)
        self.amp_contrast.setSingleStep(0.01)
        ctf_layout.addRow("Amplitude Contrast:", self.amp_contrast)
        
        self.ctf_tile = EnhancedSpinBox()
        self.ctf_tile.setRange(256, 2048)
        self.ctf_tile.setValue(512)
        self.ctf_tile.setSingleStep(256)
        ctf_layout.addRow("Tile Size:", self.ctf_tile)
        
        self.ctf_group.setLayout(ctf_layout)
        layout.addWidget(self.ctf_group, 5, 0, 1, 2)
        
        group.setLayout(layout)
        return group
        
    def get_settings(self) -> dict:
        """Get all current settings as a dictionary."""
        return {
            'motion_correction': {
                'frame_group': self.group_spin.value(),
                'patch_x': self.patch_x.value(),
                'patch_y': self.patch_y.value(),
                'max_iterations': self.iter_spin.value(),
                'tolerance': self.tol_spin.value(),
                'reference_frame': self.ref_spin.value()
            },
            'reconstruction': {
                'tilt_axis': self.tilt_spin.value(),
                'auto_refine': self.auto_refine.isChecked(),
                'volume_height': self.vol_height.value(),
                'method': self.recon_method.currentText(),
                'sart': {
                    'iterations': self.sart_iter.value(),
                    'relaxation': self.sart_relax.value()
                },
                'ctf': {
                    'amp_contrast': self.amp_contrast.value(),
                    'tile_size': self.ctf_tile.value()
                }
            }
        }
        
    def set_settings(self, settings: dict):
        """Apply settings from a dictionary."""
        mc = settings.get('motion_correction', {})
        rc = settings.get('reconstruction', {})
        
        self.group_spin.setValue(mc.get('frame_group', 1))
        self.patch_x.setValue(mc.get('patch_x', 5))
        self.patch_y.setValue(mc.get('patch_y', 5))
        self.iter_spin.setValue(mc.get('max_iterations', 15))
        self.tol_spin.setValue(mc.get('tolerance', 0.1))
        self.ref_spin.setValue(mc.get('reference_frame', -1))
        
        self.tilt_spin.setValue(rc.get('tilt_axis', 0))
        self.auto_refine.setChecked(rc.get('auto_refine', True))
        self.vol_height.setValue(rc.get('volume_height', 512))
        
        method = rc.get('method', 'SART')
        index = self.recon_method.findText(method)
        if index >= 0:
            self.recon_method.setCurrentIndex(index)
            
        sart = rc.get('sart', {})
        self.sart_iter.setValue(sart.get('iterations', 15))
        self.sart_relax.setValue(sart.get('relaxation', 0.3))
        
        ctf = rc.get('ctf', {})
        self.amp_contrast.setValue(ctf.get('amp_contrast', 0.07))
        self.ctf_tile.setValue(ctf.get('tile_size', 512))
