"""Custom spinbox implementation with clear arrows."""
from PyQt6.QtWidgets import Q<PERSON>pinBox, QDoubleSpinBox
from PyQt6.QtGui import <PERSON><PERSON>ainter, QColor, QPen
from PyQt6.QtCore import Qt, QRect

class EnhancedSpinBox(QSpinBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(30)  # Make spinbox taller
        self.setMinimumWidth(80)  # Make spinbox wider
        
    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        
        # Draw up arrow
        up_rect = QRect(self.width() - 25, 0, 25, self.height() // 2)
        self._draw_up_arrow(painter, up_rect)
        
        # Draw down arrow
        down_rect = QRect(self.width() - 25, self.height() // 2, 25, self.height() // 2)
        self._draw_down_arrow(painter, down_rect)
        
    def _draw_up_arrow(self, painter, rect):
        painter.save()
        painter.setPen(QPen(QColor("#ffffff")))
        
        # Calculate arrow points
        x = rect.center().x()
        y = rect.center().y()
        size = 6
        
        points = [
            (x, y - size),  # Top point
            (x - size, y + size),  # Bottom left
            (x + size, y + size)   # Bottom right
        ]
        
        # Draw triangle
        for i in range(3):
            painter.drawLine(
                points[i][0], points[i][1],
                points[(i + 1) % 3][0], points[(i + 1) % 3][1]
            )
        painter.restore()
        
    def _draw_down_arrow(self, painter, rect):
        painter.save()
        painter.setPen(QPen(QColor("#ffffff")))
        
        # Calculate arrow points
        x = rect.center().x()
        y = rect.center().y()
        size = 6
        
        points = [
            (x, y + size),  # Bottom point
            (x - size, y - size),  # Top left
            (x + size, y - size)   # Top right
        ]
        
        # Draw triangle
        for i in range(3):
            painter.drawLine(
                points[i][0], points[i][1],
                points[(i + 1) % 3][0], points[(i + 1) % 3][1]
            )
        painter.restore()

class EnhancedDoubleSpinBox(QDoubleSpinBox):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(30)
        self.setMinimumWidth(80)
        
    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        
        # Draw up arrow
        up_rect = QRect(self.width() - 25, 0, 25, self.height() // 2)
        self._draw_up_arrow(painter, up_rect)
        
        # Draw down arrow
        down_rect = QRect(self.width() - 25, self.height() // 2, 25, self.height() // 2)
        self._draw_down_arrow(painter, down_rect)
        
    def _draw_up_arrow(self, painter, rect):
        painter.save()
        painter.setPen(QPen(QColor("#ffffff")))
        
        # Calculate arrow points
        x = rect.center().x()
        y = rect.center().y()
        size = 6
        
        points = [
            (x, y - size),  # Top point
            (x - size, y + size),  # Bottom left
            (x + size, y + size)   # Bottom right
        ]
        
        # Draw triangle
        for i in range(3):
            painter.drawLine(
                points[i][0], points[i][1],
                points[(i + 1) % 3][0], points[(i + 1) % 3][1]
            )
        painter.restore()
        
    def _draw_down_arrow(self, painter, rect):
        painter.save()
        painter.setPen(QPen(QColor("#ffffff")))
        
        # Calculate arrow points
        x = rect.center().x()
        y = rect.center().y()
        size = 6
        
        points = [
            (x, y + size),  # Bottom point
            (x - size, y - size),  # Top left
            (x + size, y - size)   # Top right
        ]
        
        # Draw triangle
        for i in range(3):
            painter.drawLine(
                points[i][0], points[i][1],
                points[(i + 1) % 3][0], points[(i + 1) % 3][1]
            )
        painter.restore()
