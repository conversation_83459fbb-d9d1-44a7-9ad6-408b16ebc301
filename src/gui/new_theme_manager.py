#!/usr/bin/env python3
"""Theme manager for the AreTomo3 GUI."""

from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QApplication
from pathlib import Path
import json
import os

class ThemeManager(QObject):
    """Manages application themes."""
    
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.themes_dir = Path(__file__).parent / "themes"
        self.themes_dir.mkdir(exist_ok=True)
        self._load_theme_settings()
    
    def _load_theme_settings(self):
        """Load saved theme settings."""
        settings_path = self.themes_dir / "theme_settings.json"
        if settings_path.exists():
            try:
                with open(settings_path) as f:
                    settings = json.load(f)
                    self.current_theme = settings.get("current_theme", "light")
            except Exception as e:
                print(f"Error loading theme settings: {e}")
    
    def _save_theme_settings(self):
        """Save current theme settings."""
        settings_path = self.themes_dir / "theme_settings.json"
        try:
            with open(settings_path, 'w') as f:
                json.dump({"current_theme": self.current_theme}, f)
        except Exception as e:
            print(f"Error saving theme settings: {e}")
    
    def get_theme_stylesheet(self):
        """Returns the stylesheet for the current theme."""
        base_style = """
            /* SpinBox Styling */
            QSpinBox, QDoubleSpinBox {
                min-width: 80px;
                min-height: 30px;
                padding-right: 15px;
            }
            
            QSpinBox::up-button, QDoubleSpinBox::up-button,
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                width: 20px;
                background: #404040;
                border: 1px solid #505050;
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background: #505050;
            }
            
            QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
                image: none;
                width: 16px;
                height: 10px;
            }
            
            QSpinBox::up-arrow:after, QDoubleSpinBox::up-arrow:after {
                content: "▲";
                color: white;
                font-size: 12px;
                font-weight: bold;
                position: absolute;
            }
            
            QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
                image: none;
                width: 16px;
                height: 10px;
            }
            
            QSpinBox::down-arrow:after, QDoubleSpinBox::down-arrow:after {
                content: "▼";
                color: white;
                font-size: 12px;
                font-weight: bold;
                position: absolute;
            }
        """
        
        # Add theme-specific styles based on self.current_theme
        if self.current_theme == "dark":
            theme_style = """
                QSpinBox, QDoubleSpinBox {
                    background: #2d2d2d;
                    color: #ffffff;
                    border: 1px solid #404040;
                }
            """
        else:  # light theme
            theme_style = """
                QSpinBox, QDoubleSpinBox {
                    background: #ffffff;
                    color: #000000;
                    border: 1px solid #cccccc;
                }
            """
        
        return base_style + theme_style
        """Get the stylesheet for the current theme."""
        dark_theme = """
            /* Dark theme basics */
            QMainWindow, QDialog, QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            
            /* SpinBox styling */
            QSpinBox, QDoubleSpinBox {
                background-color: #353535;
                border: 1px solid #404040;
                border-radius: 4px;
                color: #ffffff;
                padding: 4px;
                min-height: 25px;
                min-width: 80px;
            }

            QSpinBox::up-button, QDoubleSpinBox::up-button {
                background-color: #505050;
                border: none;
                border-left: 1px solid #404040;
                border-bottom: 1px solid #404040;
                subcontrol-origin: margin;
                subcontrol-position: top right;
                width: 20px;
                margin-top: 1px;
                margin-right: 1px;
                border-top-right-radius: 3px;
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
                background-color: #606060;
            }
            
            QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-bottom: 7px solid white;
            }
            
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                background-color: #505050;
                border: none;
                border-left: 1px solid #404040;
                subcontrol-origin: margin;
                subcontrol-position: bottom right;
                width: 20px;
                margin-bottom: 1px;
                margin-right: 1px;
                border-bottom-right-radius: 3px;
            }
            
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #606060;
            }
            
            QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 7px solid white;
            }
        """
        
        light_theme = """
            /* Light theme basics */
            QMainWindow, QDialog, QWidget {
                background-color: #f5f5f5;
                color: #000000;
            }
            
            /* Spinbox - Basic Style */
            QSpinBox, QDoubleSpinBox {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 4px;
                color: #000000;
                padding: 4px;
                min-height: 30px;
                min-width: 90px;
            }
            
            /* Up Button */
            QSpinBox::up-button, QDoubleSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                background-color: #f0f0f0;
                border-left: 1px solid #cccccc;
                width: 25px;
                height: 15px;
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
                background-color: #e0e0e0;
            }
            
            /* Up Arrow */
            QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
                image: url("up_arrow_black.png");
                width: 10px;
                height: 10px;
            }
            
            /* Down Button */
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                background-color: #f0f0f0;
                border-left: 1px solid #cccccc;
                border-top: 1px solid #cccccc;
                width: 25px;
                height: 15px;
            }
            
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #e0e0e0;
            }
            
            /* Down Arrow */
            QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
                image: url("down_arrow_black.png");
                width: 10px;
                height: 10px;
            }
        """
        
        return dark_theme if self.current_theme == "dark" else light_theme
    
    def toggle_theme(self):
        """Toggle between light and dark themes."""
        self.current_theme = "dark" if self.current_theme == "light" else "light"
        self._save_theme_settings()
        QApplication.instance().setStyleSheet(self.get_theme_stylesheet())
        self.theme_changed.emit(self.current_theme)
