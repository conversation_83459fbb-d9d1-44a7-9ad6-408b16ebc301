#!/usr/bin/env python3
"""Theme manager for the AreTomo3 GUI."""

from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QApplication
from pathlib import Path
import json
import os

class ThemeManager(QObject):
    """Manages application themes."""
    
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.themes_dir = Path(__file__).parent / "themes"
        self.themes_dir.mkdir(exist_ok=True)
        self._load_theme_settings()
    
    def _load_theme_settings(self):
        settings_path = self.themes_dir / "theme_settings.json"
        if settings_path.exists():
            try:
                with open(settings_path) as f:
                    settings = json.load(f)
                    self.current_theme = settings.get("current_theme", "light")
            except Exception as e:
                print(f"Error loading theme settings: {e}")
    
    def _save_theme_settings(self):
        settings_path = self.themes_dir / "theme_settings.json"
        try:
            with open(settings_path, 'w') as f:
                json.dump({"current_theme": self.current_theme}, f)
        except Exception as e:
            print(f"Error saving theme settings: {e}")
    
    def get_theme_stylesheet(self):
        """Get the stylesheet for the current theme."""
        if self.current_theme == "dark":
            return """
            QMainWindow, QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            
            /* Basic SpinBox style */
            QSpinBox, QDoubleSpinBox {
                background-color: #353535;
                color: #ffffff;
                border: 1px solid #404040;
                border-radius: 4px;
                padding: 5px 35px 5px 5px;
                min-height: 25px;
                min-width: 80px;
            }
            
            /* Up button - top half of right side */
            QSpinBox::up-button, QDoubleSpinBox::up-button {
                background-color: #505050;
                width: 30px;
                border-left: 1px solid #404040;
                border-top-right-radius: 3px;
                subcontrol-origin: padding;
                subcontrol-position: right;
                top: 1px;
                bottom: 50%;
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
                background-color: #606060;
            }
            
            /* Down button - bottom half of right side */
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                background-color: #505050;
                width: 30px;
                border-left: 1px solid #404040;
                border-bottom-right-radius: 3px;
                subcontrol-origin: padding;
                subcontrol-position: right;
                top: 50%;
                bottom: 1px;
            }
            
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #606060;
            }
            """
        else:
            return """
            QMainWindow, QDialog {
                background-color: #f5f5f5;
                color: #000000;
            }
            
            QWidget {
                background-color: #f5f5f5;
                color: #000000;
            }
            
            /* Basic SpinBox style */
            QSpinBox, QDoubleSpinBox {
                background-color: #ffffff;
                color: #000000;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 5px 35px 5px 5px;
                min-height: 25px;
                min-width: 80px;
            }
            
            /* Up button - top half of right side */
            QSpinBox::up-button, QDoubleSpinBox::up-button {
                background-color: #f0f0f0;
                width: 30px;
                border-left: 1px solid #cccccc;
                border-top-right-radius: 3px;
                subcontrol-origin: padding;
                subcontrol-position: right;
                top: 1px;
                bottom: 50%;
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
                background-color: #e0e0e0;
            }
            
            /* Down button - bottom half of right side */
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                background-color: #f0f0f0;
                width: 30px;
                border-left: 1px solid #cccccc;
                border-bottom-right-radius: 3px;
                subcontrol-origin: padding;
                subcontrol-position: right;
                top: 50%;
                bottom: 1px;
            }
            
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #e0e0e0;
            }
            """
    
    def toggle_theme(self):
        self.current_theme = "dark" if self.current_theme == "light" else "light"
        self._save_theme_settings()
        QApplication.instance().setStyleSheet(self.get_theme_stylesheet())
        self.theme_changed.emit(self.current_theme)
