"""Theme manager for the AreTomo3 GUI."""

from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QApplication                   QSpinBox::up-button, QDoubleSpinBox::up-button {
                color: #ffffff;
                font-size: 14px;
                text-align: center;
                min-height: 12px;
            }
            
            QSpinBox::up-button::text, QDoubleSpinBox::up-button::text {
                content: "▲";
            }::up-arrow, QDoubleSpinBox::up-arrow {
                background-image: url("data:image/svg+xml,<svg width='12' height='12' viewBox='0 0 12 12' xmlns='http://www.w3.org/2000/svg'><path d='M2 8 L6 4 L10 8' stroke='white' fill='none' stroke-width='2'/></svg>");
                width: 12px;
                height: 12px;
            }
            
            QSpinBox::up-arrow:hover, QDoubleSpinBox::up-arrow:hover {
                background-image: url("data:image/svg+xml,<svg width='12' height='12' viewBox='0 0 12 12' xmlns='http://www.w3.org/2000/svg'><path d='M2 8 L6 4 L10 8' stroke='%23cccccc' fill='none' stroke-width='2'/></svg>");
            }pathlib import Path
import json
import os

class ThemeManager(QObject):
    """Manages application themes."""
    
    theme_changed = pyqtSignal(str)  # Signal emitted when theme changes
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.themes_dir = Path(__file__).parent / "themes"
        self.themes_dir.mkdir(exist_ok=True)
        self._load_theme_settings()
        
    def _load_theme_settings(self):
        """Load saved theme settings."""
        settings_path = self.themes_dir / "theme_settings.json"
        if settings_path.exists():
            try:
                with open(settings_path) as f:
                    settings = json.load(f)
                    self.current_theme = settings.get("current_theme", "light")
            except Exception as e:
                print(f"Error loading theme settings: {e}")
                
    def _save_theme_settings(self):
        """Save current theme settings."""
        settings_path = self.themes_dir / "theme_settings.json"
        try:
            with open(settings_path, 'w') as f:
                json.dump({"current_theme": self.current_theme}, f)
        except Exception as e:
            print(f"Error saving theme settings: {e}")
            
    def get_theme_stylesheet(self):
        """Get the stylesheet for the current theme."""
        # Common styles for both themes
        common_style = """
            /* Global Styles */
            * {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            }
            
            /* Main Window */
            QMainWindow, QDialog {
                margin: 0;
                spacing: 0;
            }
            
            /* Widgets */
            QWidget {
                margin: 2px;
                spacing: 4px;
            }
            
            /* Group Box */
            QGroupBox {
                font-weight: bold;
                border: 2px solid;
                border-radius: 6px;
                margin-top: 1.2em;
                padding: 8px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
            
            /* Buttons */
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: 500;
                min-width: 80px;
            }
            
            QPushButton:hover {
                transform: translateY(-1px);
            }
            
            QPushButton:pressed {
                transform: translateY(1px);
            }
            
            /* Input Fields */
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                padding: 6px;
                border-radius: 4px;
                min-height: 25px;
            }
            
            QSpinBox, QDoubleSpinBox {
                padding-right: 15px;
                min-width: 90px;
            }
            
            /* Labels */
            QLabel {
                font-size: 13px;
                padding: 2px;
            }
            
            /* Tabs */
            QTabWidget::pane {
                border: 2px solid;
                border-radius: 6px;
            }
            
            QTabBar::tab {
                padding: 8px 16px;
                margin: 2px;
                border-radius: 4px 4px 0 0;
            }
        """
        
        if self.current_theme == "dark":
            return common_style + """
            /* Dark theme */
            QMainWindow, QDialog {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            
            QWidget {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            
            QGroupBox {
                border-color: #404040;
                background-color: #252525;
            }
            
            QGroupBox::title {
                color: #ffffff;
                background-color: #1e1e1e;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #505050, stop:1 #404040);
                border: 1px solid #606060;
                color: #ffffff;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #606060, stop:1 #505050);
                border-color: #707070;
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #353535, stop:1 #404040);
            }
            
            /* Input Styling */
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                background-color: #2d2d2d;
                border: 1px solid #404040;
                color: #ffffff;
            }
            
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border-color: #707070;
                background-color: #353535;
            }
            
            /* Tabs */
            QTabWidget::pane {
                border-color: #404040;
                background-color: #252525;
            }
            
            QTabBar::tab {
                background-color: #2d2d2d;
                color: #b0b0b0;
            }
            
            QTabBar::tab:selected {
                background-color: #353535;
                color: #ffffff;
            }
            
            QTabBar::tab:hover:!selected {
                background-color: #323232;
                color: #ffffff;
            }
            
            /* Scrollbars */
            QScrollBar:vertical {
                background: #2d2d2d;
                width: 14px;
                margin: 0px;
            }
            
            QScrollBar::handle:vertical {
                background: #505050;
                min-height: 20px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #606060;
            }
            
            QScrollBar:horizontal {
                background: #2d2d2d;
                height: 14px;
                margin: 0px;
            }
            
            QScrollBar::handle:horizontal {
                background: #505050;
                min-width: 20px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background: #606060;
            }
            
            QLineEdit, QSpinBox, QDoubleSpinBox {
                background-color: #353535;
                border: 1px solid #404040;
                border-radius: 4px;
                color: #ffffff;
                padding: 3px;
            }
            
            /* Simplified spinbox approach - no custom arrows */
            QSpinBox, QDoubleSpinBox {
                background-color: #353535;
                border: 1px solid #404040;
                border-radius: 4px;
                color: #ffffff;
                padding: 4px;
                min-width: 75px;
                min-height: 25px;
            }
            
            QSpinBox::up-button, QDoubleSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: top right;
                width: 25px;
                height: 12px;
                border-left: 1px solid #404040;
                background-color: #505050;
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
                background-color: #606060;
            }
            
            QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed {
                background-color: #404040;
            }
            
            QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-bottom: 6px solid #ffffff;
                margin: 3px;
            }
            
            QSpinBox::up-arrow:hover, QDoubleSpinBox::up-arrow:hover {
                border-color: #cccccc;
            }
            
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: right;
                width: 22px;
                border-left: 1px solid #404040;
                background-color: #505050;
                border-top-right-radius: 0;
                border-bottom-right-radius: 4px;
            }
            
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #606060;
            }
            
            QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
                background-color: #404040;
            }
            
            QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #ffffff;
                margin: 3px;
            }
            
            QSpinBox::down-arrow:hover, QDoubleSpinBox::down-arrow:hover {
                border-color: #cccccc;
            }
            
            QTextEdit {
                background-color: #353535;
                border: 1px solid #404040;
                border-radius: 4px;
                color: #ffffff;
            }
            
            QProgressBar {
                border: 1px solid #404040;
                border-radius: 4px;
                text-align: center;
            }
            
            QProgressBar::chunk {
                background-color: #2a82da;
            }
            
            QTabWidget::pane {
                border: 1px solid #404040;
            }
            
            QTabBar::tab {
                background-color: #353535;
                border: 1px solid #404040;
                padding: 5px;
            }
            
            QTabBar::tab:selected {
                background-color: #404040;
            }
            """
        else:  # Light theme
            return common_style + """
            /* Light theme */
            QMainWindow, QDialog {
                background-color: #ffffff;
                color: #2d2d2d;
            }
            
            QWidget {
                background-color: #ffffff;
                color: #2d2d2d;
            }
            
            QGroupBox {
                border-color: #e0e0e0;
                background-color: #fafafa;
            }
            
            QGroupBox::title {
                color: #2d2d2d;
                background-color: #ffffff;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #ced4da;
                color: #2d2d2d;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #e9ecef, stop:1 #dee2e6);
                border-color: #adb5bd;
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #dee2e6, stop:1 #e9ecef);
            }
            
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                background-color: #ffffff;
                border: 1px solid #ced4da;
                color: #2d2d2d;
            }
            
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border-color: #80bdff;
                background-color: #ffffff;
            }
            
            QTabWidget::pane {
                border-color: #dee2e6;
                background-color: #ffffff;
            }
            
            QTabBar::tab {
                background-color: #f8f9fa;
                color: #6c757d;
            }
            
            QTabBar::tab:selected {
                background-color: #ffffff;
                color: #2d2d2d;
            }
            
            QTabBar::tab:hover:!selected {
                background-color: #e9ecef;
                color: #2d2d2d;
            }
            
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 14px;
                margin: 0px;
            }
            
            QScrollBar::handle:vertical {
                background: #adb5bd;
                min-height: 20px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #6c757d;
            }
            
            QScrollBar:horizontal {
                background: #f8f9fa;
                height: 14px;
                margin: 0px;
            }
            
            QScrollBar::handle:horizontal {
                background: #adb5bd;
                min-width: 20px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background: #6c757d;
            }
            """
            
    def toggle_theme(self):
        """Toggle between light and dark themes."""
        self.current_theme = "dark" if self.current_theme == "light" else "light"
        self._save_theme_settings()
        QApplication.instance().setStyleSheet(self.get_theme_stylesheet())
        self.theme_changed.emit(self.current_theme)
