#!/usr/bin/env python3
"""
Main entry point for AreTomo3 GUI application.
"""
import sys
import os
import logging
import traceback
import signal
import matplotlib
matplotlib.use('qtagg')  # Set the backend before importing any matplotlib modules

from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from .gui import AreTomoGUI
from .core.logging_config import setup_logging

logger = logging.getLogger(__name__)

def excepthook(exc_type, exc_value, exc_traceback):
    """Handle uncaught exceptions"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger.critical("Uncaught exception occurred:",
                    exc_info=(exc_type, exc_value, exc_traceback))
    
    # Show error dialog in GUI mode
    if QApplication.instance() is not None:
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Icon.Critical)
        msg.setWindowTitle("Critical Error")
        msg.setText("An unexpected error occurred and the application will close.")
        msg.setDetailedText(''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
        msg.exec()
    
    sys.exit(1)

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    if QApplication.instance() is not None:
        QApplication.instance().quit()
    sys.exit(0)

def main():
    """Main entry point"""
    # Install signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Install exception hook
    sys.excepthook = excepthook
    
    # Set up logging
    setup_logging()
    logger.info("Starting AreTomo3 GUI...")
    
    try:
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("AreTomo3 GUI")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("AreTomo3 Team")
        app.setOrganizationDomain("aretomo3.org")
        
        # Allow the app to quit when the last window is closed
        app.setQuitOnLastWindowClosed(True)
        
        # Enable high DPI scaling (PyQt6 handles this automatically, but we can set policies)
        try:
            # These attributes were deprecated in PyQt6, DPI scaling is automatic now
            pass
        except AttributeError:
            pass  # PyQt6 handles DPI scaling automatically
        
        # Create and show main window
        main_window = AreTomoGUI()
        main_window.show()
        
        logger.info("AreTomo3 GUI started successfully")
        
        # Start the event loop
        sys.exit(app.exec())
        
    except ImportError as e:
        error_msg = f"Missing dependencies: {str(e)}"
        logger.error(error_msg)
        print(f"Error: {error_msg}")
        print("Please install the required dependencies:")
        print("pip install -r requirements.txt")
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"Failed to start application: {str(e)}"
        logger.error(error_msg)
        print(f"Error: {error_msg}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
