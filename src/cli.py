#!/usr/bin/env python3
"""
Command-line interface for AreTomo3 GUI.
"""
import sys
import argparse
import logging
from typing import Optional, List
from pathlib import Path

from .core.logging_config import setup_logging
from .core.config.config_manager import ConfigManager
from .utils.mdoc_parser import parse_mdoc_file
from .core.error_handling import handle_error

logger = logging.getLogger(__name__)

def setup_cli_logging(verbose: bool = False) -> None:
    """Set up logging for CLI usage."""
    level = logging.DEBUG if verbose else logging.INFO
    setup_logging(level=level, console_only=True)

def process_files(input_files: List[Path], output_dir: Optional[Path] = None, 
                 config_file: Optional[Path] = None) -> bool:
    """Process input files using AreTomo3."""
    try:
        # Load configuration
        config_manager = ConfigManager()
        if config_file:
            config_manager.load_config(config_file)
        
        # Process each file
        for input_file in input_files:
            logger.info(f"Processing {input_file}")
            
            if input_file.suffix.lower() == '.mdoc':
                # Parse MDOC file
                mdoc_data = parse_mdoc_file(input_file)
                logger.info(f"Parsed MDOC file with {len(mdoc_data.get('sections', []))} sections")
            
            # TODO: Add actual processing logic here
            logger.info(f"Would process {input_file} (processing logic not implemented)")
        
        return True
        
    except Exception as e:
        handle_error(e, f"Failed to process files: {input_files}")
        return False

def list_configs() -> None:
    """List available configurations."""
    try:
        config_manager = ConfigManager()
        configs = config_manager.list_configurations()
        
        if not configs:
            print("No configurations found.")
            return
        
        print("Available configurations:")
        for config in configs:
            print(f"  - {config}")
            
    except Exception as e:
        handle_error(e, "Failed to list configurations")

def validate_config(config_file: Path) -> bool:
    """Validate a configuration file."""
    try:
        config_manager = ConfigManager()
        is_valid = config_manager.validate_config_file(config_file)
        
        if is_valid:
            print(f"Configuration file {config_file} is valid.")
        else:
            print(f"Configuration file {config_file} is invalid.")
        
        return is_valid
        
    except Exception as e:
        handle_error(e, f"Failed to validate configuration file: {config_file}")
        return False

def create_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="AreTomo3 GUI Command Line Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s process input.mrc -o output/
  %(prog)s process *.mrc --config my_config.json
  %(prog)s list-configs
  %(prog)s validate-config config.json
        """
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Process command
    process_parser = subparsers.add_parser("process", help="Process input files")
    process_parser.add_argument(
        "input_files",
        nargs="+",
        type=Path,
        help="Input files to process"
    )
    process_parser.add_argument(
        "--output", "-o",
        type=Path,
        help="Output directory"
    )
    process_parser.add_argument(
        "--config", "-c",
        type=Path,
        help="Configuration file to use"
    )
    
    # List configs command
    list_parser = subparsers.add_parser("list-configs", help="List available configurations")
    
    # Validate config command
    validate_parser = subparsers.add_parser("validate-config", help="Validate a configuration file")
    validate_parser.add_argument(
        "config_file",
        type=Path,
        help="Configuration file to validate"
    )
    
    return parser

def main() -> None:
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    # Set up logging
    setup_cli_logging(args.verbose)
    
    try:
        if args.command == "process":
            # Validate input files
            for input_file in args.input_files:
                if not input_file.exists():
                    logger.error(f"Input file does not exist: {input_file}")
                    sys.exit(1)
            
            success = process_files(args.input_files, args.output, args.config)
            sys.exit(0 if success else 1)
            
        elif args.command == "list-configs":
            list_configs()
            
        elif args.command == "validate-config":
            if not args.config_file.exists():
                logger.error(f"Configuration file does not exist: {args.config_file}")
                sys.exit(1)
            
            is_valid = validate_config(args.config_file)
            sys.exit(0 if is_valid else 1)
            
        else:
            parser.print_help()
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        handle_error(e, "CLI operation failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
