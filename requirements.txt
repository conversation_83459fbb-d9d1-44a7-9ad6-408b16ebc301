# AT3GUI Core Dependencies
# Production dependencies for AreTomo3 GUI
# Install with: pip install -r requirements.txt

# GUI Framework
PyQt6>=6.4.0,<7.0.0

# Scientific Computing
numpy>=1.21.0,<2.0.0
scipy>=1.7.0,<2.0.0
matplotlib>=3.5.0,<4.0.0

# File Format Support
mrcfile>=1.4.0,<2.0.0
h5py>=3.6.0,<4.0.0
tifffile>=2021.11.2,<2025.0.0
imageio>=2.19.0,<3.0.0

# Image Processing
Pillow>=8.3.0,<11.0.0

# System Monitoring
psutil>=5.8.0,<6.0.0

# Configuration
PyYAML>=6.0

# Build Tools
setuptools>=60.0.0
typing-extensions>=4.0.0,<5.0.0
