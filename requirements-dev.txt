# Development dependencies for AreTomo3 GUI
# Install with: pip install -r requirements-dev.txt

# Import main requirements
-r requirements.txt

# Testing
pytest>=8.0.0,<9.0.0
pytest-qt>=4.4.0,<5.0.0
pytest-asyncio>=0.26.0,<0.27.0
pytest-cov>=6.1.1,<7.0.0

# Type checking
mypy>=1.9.0,<2.0.0
types-setuptools>=69.0.0,<70.0.0
types-psutil>=5.9.0,<6.0.0

# Code quality
flake8>=7.0.0,<8.0.0
black>=24.2.0,<25.0.0
isort>=5.13.0,<6.0.0

# Documentation
sphinx>=7.2.0,<8.0.0
sphinx-rtd-theme>=2.0.0,<3.0.0

# Build tools
build>=1.0.0,<2.0.0
wheel>=0.42.0,<0.43.0
twine>=4.0.0,<5.0.0
