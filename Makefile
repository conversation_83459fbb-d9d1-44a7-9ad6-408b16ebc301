.PHONY: install test lint clean build

install:
	pip install -e ".[dev]"

test:
	python -m pytest tests/ --cov=src

lint:
	mypy src/
	flake8 src/

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info
	find . -type d -name __pycache__ -exec rm -r {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "htmlcov" -exec rm -r {} +
	find . -type d -name ".pytest_cache" -exec rm -r {} +
	find . -type d -name ".mypy_cache" -exec rm -r {} +

build: clean
	python setup.py sdist bdist_wheel

publish: build
	twine check dist/*
	twine upload dist/*
