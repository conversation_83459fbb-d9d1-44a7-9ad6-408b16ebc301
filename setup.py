"""Setup configuration for AreTomo3 GUI package."""

import os
from setuptools import setup, find_packages

# Read the contents of README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [
        line.strip()
        for line in fh
        if line.strip() and not line.startswith("#")
    ]

def get_version():
    """Get version from __init__.py without importing the package."""
    init_path = os.path.join("aretomo3_gui", "__init__.py")
    with open(init_path) as f:
        for line in f:
            if line.startswith('__version__'):
                return line.split('=')[1].strip().strip('"').strip("'")
    raise RuntimeError(f"Cannot find version string in {init_path}")

setup(
    name="aretomo3-gui",
    version=get_version(),
    author="AreTomo3 GUI Team",
    author_email="<EMAIL>",
    description="A graphical interface for AreTomo3 tilt series alignment and reconstruction",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/aretomo3/aretomo3-gui",
    project_urls={
        "Bug Tracker": "https://github.com/aretomo3/aretomo3-gui/issues",
        "Documentation": "https://aretomo3-gui.readthedocs.io/",
        "Source Code": "https://github.com/aretomo3/aretomo3-gui",
    },
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.8,<4.0",
    install_requires=requirements,
    include_package_data=True,
    package_data={
        "aretomo3_gui": [
            "gui/themes/*.qss",
            "utils/*.sh",
            "config/*.json",
            "docs/*.md",
        ],
    },
    entry_points={
        "console_scripts": [
            "aretomo3-gui=aretomo3_gui.main:main",
            "aretomo3-cli=aretomo3_gui.cli:main",
        ],
        "gui_scripts": [
            "aretomo3-gui-app=aretomo3_gui.main:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Environment :: X11 Applications :: Qt",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3 :: Only",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Bio-Informatics",
        "Topic :: Scientific/Engineering :: Visualization",
    ],
    keywords="electron microscopy, tomography, cryo-em, image processing",
)