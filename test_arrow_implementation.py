#!/usr/bin/env python3
"""
TEST: QSpinBox Arrow Implementation Fix
=====================================

This script tests the fixed QSpinBox and QDoubleSpinBox arrow implementations
to ensure they show proper triangular arrows instead of black rectangles.

The fix replaces solid background-color arrows with CSS border triangles
that create proper directional arrow shapes.
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QSpinBox, QDoubleSpinBox, QLabel, QPushButton,
                            QGroupBox)
from PyQt6.QtCore import Qt

# Add both possible theme manager paths
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aretomo3_gui'))

try:
    from gui.theme_manager import ThemeManager
    print("✓ Loaded theme manager from src/gui/")
except ImportError:
    try:
        from aretomo3_gui.gui.theme_manager import ThemeManager
        print("✓ Loaded theme manager from aretomo3_gui/gui/")
    except ImportError as e:
        print(f"✗ Could not import theme manager: {e}")
        sys.exit(1)

class ArrowTestWindow(QMainWindow):
    """Test window for spinbox arrow visibility."""
    
    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager()
        self.init_ui()
        self.apply_theme()
        
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("QSpinBox Arrow Implementation Test")
        self.setGeometry(300, 300, 600, 500)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("QSpinBox Arrow Implementation Test")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 15px;")
        layout.addWidget(title)
        
        # Test description
        desc = QLabel(
            "This test verifies that spinbox arrows are now properly implemented\n"
            "as triangular shapes instead of black rectangles or colored squares."
        )
        desc.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc.setWordWrap(True)
        layout.addWidget(desc)
        
        # Spinbox test group
        spinbox_group = QGroupBox("SpinBox Controls")
        spinbox_layout = QVBoxLayout(spinbox_group)
        
        # Standard integer spinbox
        int_layout = QHBoxLayout()
        int_layout.addWidget(QLabel("Integer SpinBox:"))
        int_spinbox = QSpinBox()
        int_spinbox.setRange(-999, 999)
        int_spinbox.setValue(42)
        int_spinbox.setMinimumHeight(30)
        int_layout.addWidget(int_spinbox)
        spinbox_layout.addLayout(int_layout)
        
        # Double precision spinbox
        double_layout = QHBoxLayout()
        double_layout.addWidget(QLabel("Double SpinBox:"))
        double_spinbox = QDoubleSpinBox()
        double_spinbox.setRange(-999.99, 999.99)
        double_spinbox.setValue(3.14)
        double_spinbox.setDecimals(2)
        double_spinbox.setMinimumHeight(30)
        double_layout.addWidget(double_spinbox)
        spinbox_layout.addLayout(double_layout)
        
        # Large spinbox for better arrow visibility
        large_layout = QHBoxLayout()
        large_layout.addWidget(QLabel("Large SpinBox:"))
        large_spinbox = QSpinBox()
        large_spinbox.setRange(0, 10000)
        large_spinbox.setValue(500)
        large_spinbox.setStyleSheet("font-size: 16px; min-height: 40px;")
        large_layout.addWidget(large_spinbox)
        spinbox_layout.addLayout(large_layout)
        
        layout.addWidget(spinbox_group)
        
        # Theme switching buttons
        theme_group = QGroupBox("Theme Testing")
        theme_layout = QHBoxLayout(theme_group)
        
        dark_btn = QPushButton("Dark Theme")
        dark_btn.clicked.connect(lambda: self.switch_theme("dark"))
        theme_layout.addWidget(dark_btn)
        
        light_btn = QPushButton("Light Theme")
        light_btn.clicked.connect(lambda: self.switch_theme("light"))
        theme_layout.addWidget(light_btn)
        
        layout.addWidget(theme_group)
        
        # Expected results
        results_group = QGroupBox("Expected Results")
        results_layout = QVBoxLayout(results_group)
        
        results_text = QLabel(
            "✓ Up arrows should point upward (triangle ▲)\n"
            "✓ Down arrows should point downward (triangle ▼)\n"
            "✓ Dark theme: White arrows on gray buttons\n"
            "✓ Light theme: Dark gray arrows on light buttons\n"
            "✓ Hover effects should change arrow color slightly\n"
            "✓ No black rectangles or solid squares visible"
        )
        results_text.setStyleSheet("background-color: rgba(100,200,100,0.1); padding: 10px; border-radius: 5px;")
        results_layout.addWidget(results_text)
        
        layout.addWidget(results_group)
        
        layout.addStretch()
        
    def switch_theme(self, theme_name):
        """Switch to the specified theme."""
        print(f"Switching to {theme_name} theme...")
        self.theme_manager.set_theme(theme_name)
        self.apply_theme()
        
    def apply_theme(self):
        """Apply the current theme to the application."""
        stylesheet = self.theme_manager.get_theme_stylesheet()
        self.setStyleSheet(stylesheet)
        print(f"Applied {self.theme_manager.current_theme} theme")

def main():
    """Run the arrow test application."""
    app = QApplication(sys.argv)
    
    print("QSpinBox Arrow Implementation Test")
    print("=" * 50)
    print("Testing CSS border triangle arrow implementation...")
    print("- CSS technique: border triangles with transparent sides")
    print("- Up arrow: border-bottom creates upward triangle")
    print("- Down arrow: border-top creates downward triangle")
    print("=" * 50)
    
    # Create and show test window
    window = ArrowTestWindow()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
