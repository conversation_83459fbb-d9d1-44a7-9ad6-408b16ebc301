# 🚀 AT3GUI - External User Installation Guide

## 📋 **For Fresh Ubuntu/CentOS Systems**

This guide is designed for external users with newly installed Ubuntu or CentOS systems who want to install and test AT3GUI.

---

## 🎯 **Quick Start (One Command)**

### **For Fresh Systems (Recommended)**
```bash
# Download AT3GUI package and extract it
# Then run:
./setup_at3gui.sh --system-deps --desktop --test
```

This single command will:
- ✅ Install all system dependencies (Python, Qt, etc.)
- ✅ Create isolated virtual environment
- ✅ Install all Python dependencies
- ✅ Test the installation
- ✅ Create desktop launcher
- ✅ Verify everything works

---

## 📋 **System Requirements**

### **Supported Operating Systems**
- **Ubuntu**: 18.04, 20.04, 22.04, 24.04
- **CentOS**: 7, 8, 9
- **RHEL**: 7, 8, 9
- **Fedora**: 35+
- **Debian**: 10, 11, 12

### **Minimum Hardware**
- **CPU**: 2+ cores
- **RAM**: 4GB (8GB+ recommended for large tomograms)
- **Storage**: 2GB free space
- **Display**: 1024x768 (1920x1080+ recommended)

### **Network Requirements**
- Internet connection for downloading dependencies (~150MB)
- No special firewall configuration needed

---

## 🔧 **Installation Methods**

### **Method 1: Automated Installation (Recommended)**

#### **Step 1: Download AT3GUI**
```bash
# Option A: Download from repository
git clone <repository-url> AT3GUI
cd AT3GUI

# Option B: Extract from archive
tar -xzf AT3GUI.tar.gz
cd AT3GUI
```

#### **Step 2: Run Installation Script**
```bash
# For fresh systems (installs everything)
./setup_at3gui.sh --system-deps --desktop --test

# For systems with Python already installed
./setup_at3gui.sh --desktop --test

# For development installation
./setup_at3gui.sh --system-deps --dev --test
```

#### **Step 3: Launch AT3GUI**
```bash
# Activate virtual environment
source venv/bin/activate

# Launch AT3GUI
python launch_at3gui.py
```

### **Method 2: Manual Installation**

#### **Step 1: Install System Dependencies**

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install -y \
    python3 python3-pip python3-venv python3-dev \
    build-essential pkg-config \
    libgl1-mesa-glx libglib2.0-0 \
    libxkbcommon-x11-0 libxcb-icccm4 \
    libxcb-image0 libxcb-keysyms1 \
    libxcb-randr0 libxcb-render-util0 \
    libxcb-xinerama0 libxcb-xfixes0 \
    libfontconfig1 libxrender1 \
    libxi6 libxext6 libx11-6 \
    git wget curl xvfb
```

**CentOS/RHEL/Fedora:**
```bash
# For CentOS/RHEL 8+, Fedora
sudo dnf install -y \
    python3 python3-pip python3-devel \
    gcc gcc-c++ make pkgconfig \
    mesa-libGL glib2 \
    libxkbcommon-x11 xcb-util-icccm \
    xcb-util-image xcb-util-keysyms \
    xcb-util-renderutil xcb-util-wm \
    fontconfig libXrender libXi \
    libXext libX11 \
    git wget curl xorg-x11-server-Xvfb

# For CentOS/RHEL 7
sudo yum install -y [same packages with yum]
```

#### **Step 2: Create Virtual Environment**
```bash
cd AT3GUI
python3 -m venv venv --prompt "AT3GUI"
source venv/bin/activate
```

#### **Step 3: Install Python Dependencies**
```bash
pip install --upgrade pip setuptools wheel
pip install -r requirements.txt
```

#### **Step 4: Test Installation**
```bash
python tests/validation/test_comprehensive_imports.py
python launch_at3gui.py
```

---

## 🧪 **Testing Your Installation**

### **Quick Tests**
```bash
# Activate virtual environment
source venv/bin/activate

# Test 1: Basic import
python -c "import aretomo3_gui; print('✅ AT3GUI imported successfully')"

# Test 2: Core functionality
python -c "from aretomo3_gui.core.logging_config import setup_logging; print('✅ Core modules working')"

# Test 3: GUI components (requires display)
python -c "from aretomo3_gui.gui.main_window import AreTomoGUI; print('✅ GUI components available')"
```

### **Comprehensive Tests**
```bash
# Run full test suite
python run_tests.py

# Run specific test categories
python tests/validation/test_comprehensive_imports.py
python tests/integration/test_application_startup.py
python tests/integration/test_mrc_viewer.py
```

### **Test with Sample Data**
```bash
# Load the included 599.7 MB test tomogram
python launch_at3gui.py
# Then: File → Open → rec_TS_85.mrc
```

---

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Issue 1: "Python 3 is not installed"**
**Solution:**
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3 python3-pip python3-venv

# CentOS/RHEL/Fedora
sudo dnf install python3 python3-pip
```

#### **Issue 2: "Permission denied" when running setup script**
**Solution:**
```bash
chmod +x setup_at3gui.sh
./setup_at3gui.sh --system-deps
```

#### **Issue 3: Return code 132 (Display issues)**
**Solutions:**
```bash
# Option 1: Use offscreen rendering
export QT_QPA_PLATFORM=offscreen
python launch_at3gui.py

# Option 2: Use virtual display
sudo apt-get install xvfb  # Ubuntu
xvfb-run -a python launch_at3gui.py

# Option 3: For SSH connections
ssh -X username@hostname
```

#### **Issue 4: "No module named 'PyQt6'"**
**Solution:**
```bash
# Ensure virtual environment is activated
source venv/bin/activate

# Reinstall dependencies
pip install -r requirements.txt
```

#### **Issue 5: "Cannot import name 'ResourceManager'"**
**Solution:**
```bash
# This is a known non-critical issue
# AT3GUI will still work for basic functionality
# Run with --dev flag for full features:
./setup_at3gui.sh --dev
```

### **Getting Help**

#### **Check Installation Status**
```bash
# Verify virtual environment
echo $VIRTUAL_ENV  # Should show /path/to/AT3GUI/venv

# Check installed packages
pip list | grep -E "(PyQt6|numpy|matplotlib|mrcfile)"

# Check Python path
python -c "import sys; print(sys.path)"
```

#### **Debug Mode**
```bash
# Run with debug output
python -c "
import os
os.environ['QT_DEBUG_PLUGINS'] = '1'
from aretomo3_gui.main import main
main()
"
```

#### **Log Files**
```bash
# Check application logs
ls -la logs/
cat logs/aretomo3_gui_*.log
```

---

## 📊 **Expected Results**

### **Successful Installation Should Show:**
```
🎉 AT3GUI Installation Complete!
==============================================

✅ Installation successful in virtual environment
ℹ️  Virtual environment: /path/to/AT3GUI/venv
ℹ️  Python version: 3.x.x
ℹ️  Dependencies: 25+ packages installed

🚀 How to Launch AT3GUI:
1. Activate virtual environment:
   source venv/bin/activate

2. Launch AT3GUI (choose one):
   python launch_at3gui.py          # Recommended
   python -m aretomo3_gui.main      # Alternative

✅ AT3GUI is ready for use!
```

### **Test Results Should Show:**
```
🔬 AT3GUI Comprehensive Import Testing
============================================================
✅ Basic Imports..................... PASS
✅ Core Modules...................... PASS
✅ Configuration System.............. PASS
✅ GUI Components.................... PASS
✅ Viewer Components................. PASS
✅ Widget Components................. PASS
✅ Utility Functions................. PASS
✅ Main Entry Point.................. PASS

🎯 Overall Success Rate: 8/9 (88.9%+)
🎉 EXCELLENT! Import tests passed with flying colors!
```

---

## 🎉 **Success! What's Next?**

### **Launch AT3GUI**
```bash
source venv/bin/activate
python launch_at3gui.py
```

### **Load Sample Data**
1. **File → Open**
2. **Select: rec_TS_85.mrc** (599.7 MB test tomogram)
3. **Explore the enhanced Analysis and Viewer tabs**

### **Key Features to Try**
- **Enhanced Analysis Tab**: 8 analysis types
- **Advanced Viewer Tab**: 3D tomogram visualization
- **Measurement Tools**: Distance and angle measurement
- **Export Functions**: Multiple format support
- **Professional Themes**: Dark/light mode switching

---

## 📞 **Support**

### **If You Need Help**
1. **Check logs**: `cat logs/aretomo3_gui_*.log`
2. **Run diagnostics**: `python run_tests.py`
3. **Try troubleshooting steps** above
4. **Contact support** with error messages and system info

### **System Information for Support**
```bash
# Gather system info for support
echo "OS: $(cat /etc/os-release | grep PRETTY_NAME)"
echo "Python: $(python3 --version)"
echo "Virtual Env: $VIRTUAL_ENV"
echo "AT3GUI Path: $(pwd)"
pip list | grep -E "(PyQt6|numpy|matplotlib)"
```

**AT3GUI is ready for professional use!** 🚀
