#!/usr/bin/env python3
"""
AT3GUI Launcher Script
Simple launcher for the AreTomo3 GUI application.
"""

import sys
import os
from pathlib import Path

def main():
    """Launch AT3GUI with proper error handling."""
    print("🚀 Launching AT3GUI...")
    print("=" * 50)
    
    # Add current directory to Python path
    current_dir = Path(__file__).parent.absolute()
    sys.path.insert(0, str(current_dir))
    
    try:
        # Import and run the main application
        from aretomo3_gui.main import main as app_main
        print("✅ AT3GUI modules imported successfully")
        print("🎨 Starting GUI application...")
        
        # Launch the application
        return app_main()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure you're in the AT3GUI directory")
        print("2. Check that all dependencies are installed:")
        print("   pip install PyQt6 numpy matplotlib mrcfile psutil")
        print("3. Verify the aretomo3_gui package structure")
        return 1
        
    except Exception as e:
        print(f"❌ Error launching AT3GUI: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
