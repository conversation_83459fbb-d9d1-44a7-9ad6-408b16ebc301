#!/usr/bin/env python3
"""
AT3GUI Launcher Script
Professional launcher for the AreTomo3 GUI application with environment checking.
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are available."""
    required_packages = [
        'PyQt6', 'numpy', 'matplotlib', 'mrcfile', 'psutil'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.lower())
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ Missing required packages:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n🔧 Install missing packages with:")
        print("   pip install -r requirements.txt")
        return False

    return True

def check_virtual_environment():
    """Check if we're in the correct virtual environment."""
    current_dir = Path(__file__).parent.absolute()
    venv_path = current_dir / "venv"

    # Check if virtual environment exists
    if not venv_path.exists():
        print("❌ Virtual environment not found!")
        print("🔧 Please run the setup script first:")
        print("   ./setup_at3gui.sh")
        return False

    # Check if we're in the virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # We're in a virtual environment
        expected_venv = str(venv_path)
        current_venv = os.environ.get('VIRTUAL_ENV', '')

        if current_venv != expected_venv:
            print("⚠️  Wrong virtual environment detected!")
            print(f"   Expected: {expected_venv}")
            print(f"   Current:  {current_venv}")
            print("🔧 Please activate the correct virtual environment:")
            print("   source venv/bin/activate")
            return False
        else:
            print(f"✅ Virtual environment active: {current_venv}")
            return True
    else:
        print("❌ Virtual environment not activated!")
        print("🔧 Please activate the virtual environment:")
        print("   source venv/bin/activate")
        print("   python launch_at3gui.py")
        return False

def setup_environment():
    """Set up the environment for AT3GUI."""
    # Add current directory to Python path
    current_dir = Path(__file__).parent.absolute()
    sys.path.insert(0, str(current_dir))

    # Set environment variables for better performance
    os.environ.setdefault('QT_AUTO_SCREEN_SCALE_FACTOR', '1')
    os.environ.setdefault('QT_ENABLE_HIGHDPI_SCALING', '1')

def main():
    """Launch AT3GUI with comprehensive error handling."""
    print("🚀 AT3GUI Launcher")
    print("=" * 50)

    # Check Python version
    if not check_python_version():
        return 1

    print("✅ Python version compatible")

    # Check virtual environment
    if not check_virtual_environment():
        return 1

    # Set up environment
    setup_environment()
    print("✅ Environment configured")

    # Check dependencies
    if not check_dependencies():
        return 1

    print("✅ All dependencies available")

    try:
        # Import and run the main application
        from aretomo3_gui.main import main as app_main
        print("✅ AT3GUI modules imported successfully")
        print("🎨 Starting GUI application...")
        print("   (This may take a few seconds...)")

        # Launch the application
        return app_main()

    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure you're in the AT3GUI directory")
        print("2. Install dependencies: pip install -r requirements.txt")
        print("3. Try: python -m aretomo3_gui.main")
        return 1

    except Exception as e:
        print(f"❌ Error launching AT3GUI: {e}")
        print("\n📋 Debug Information:")
        import traceback
        traceback.print_exc()
        print("\n🆘 For support, please share this error message")
        return 1

if __name__ == "__main__":
    sys.exit(main())
