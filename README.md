# AreTomo3 GUI

A comprehensive graphical interface for AreTomo3 tilt series alignment and reconstruction in cryo-electron microscopy. This application provides an intuitive interface for processing electron microscopy tilt series data, with support for batch processing, real-time monitoring, and advanced visualization tools.

[![Python Version](https://img.shields.io/pypi/pyversions/aretomo3-gui.svg)](https://pypi.org/project/aretomo3-gui/)
[![PyPI version](https://badge.fury.io/py/aretomo3-gui.svg)](https://badge.fury.io/py/aretomo3-gui)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## Features

- 🎯 Intuitive interface for AreTomo3 parameter configuration
- 📊 Real-time system resource monitoring (CPU, RAM, and GPU usage)
- 🔄 Advanced batch processing system for multiple tilt series
- 📈 Live progress updates and process monitoring
- 🔍 Integrated MRC file viewer with analysis tools
- 📊 Motion correction and CTF data analysis
- 💾 Multi-format export (IMOD, TIFF, Relion, EMAN2, ImageJ)
- 🌙 Dark mode support
- 💾 Session state persistence
- 🛡️ Comprehensive error handling and validation

## Project Structure

The project follows a modular architecture:

```plaintext
aretomo3_gui/
├── src/
│   ├── core/           # Core functionality
│   │   ├── config/     # Configuration management
│   │   ├── error_handling.py
│   │   ├── logging_config.py
│   │   ├── resource_manager.py
│   │   └── thread_manager.py
│   ├── gui/            # GUI components
│   │   ├── viewers/    # Visualization components
│   │   └── widgets/    # Reusable UI widgets
│   └── utils/          # Utility functions
├── tests/              # Test suite
├── logs/               # Application logs
└── Test_Input_*/      # Test data directories
```

## Installation

### Using pip

The easiest way to install AreTomo3 GUI is via pip:

```bash
pip install aretomo3-gui
```

After installation, you can run the application with:

```bash
aretomo3-gui
```

### Development Installation

For development or to run from source:

1. Clone the repository:
```bash
git clone https://github.com/yourusername/aretomo3-gui.git
cd aretomo3-gui
```

2. Create and activate virtual environment:
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# or
.venv\Scripts\activate     # Windows
```

3. Install in development mode:
```bash
pip install -e ".[dev]"
```

4. Run the application:
```bash
python -m aretomo3_gui
```

## System Requirements

### Software Requirements
- Python 3.8 or higher
- Qt 6.9.0 or higher
- CUDA toolkit (for GPU support)
- AreTomo3 executable

### Hardware Requirements
- 16GB RAM minimum (32GB recommended)
- NVIDIA GPU with CUDA support (recommended)
- Storage space for data processing

## Development Guidelines

The project follows these development practices:

* Code follows PEP 8 style guidelines
* All modules, classes, and functions have docstrings
* Tests are run using pytest: `python -m pytest tests/`

## Key Features

* Tilt series alignment and reconstruction
* Real-time processing status
* Resource monitoring (CPU, GPU, Memory)
* Dark mode support
* Session state persistence

## System Requirements

* Python 3.8+
* PyQt6
* Required GPU drivers for AreTomo3

## Error Handling System

The application implements comprehensive error handling:

* Structured exception hierarchy
* Automatic error logging
* User-friendly error dialogs
* Resource cleanup on errors

## Configuration Management

Application configuration is managed through:

* Configuration files in `src/core/config/`
* Command line arguments
* User preferences persistence

## Features

- User-friendly interface for AreTomo3 parameter configuration
- Automatic handling of tilt series data
- Real-time system resource monitoring (RAM and GPU usage)
- Queue system for batch processing
- Live progress updates
- Integrated MRC file viewer
- Analysis tools for motion correction and CTF data
- **NEW**: Batch processing system for multiple tilt series
- **NEW**: Enhanced error handling and validation
- **NEW**: Export functionality for multiple formats (IMOD, TIFF, Relion, EMAN2, ImageJ)

## Recent Improvements

- Completed implementation of all core functionality
- Added robust error handling throughout the application
- Fixed syntax and logical errors in the codebase
- Implemented batch processing for multiple tilt series
- Enhanced system resource monitoring with GPU support
- Improved UI and user experience
- Added export functionality for scientific format conversion

## Prerequisites

- Python 3.8 or newer
- AreTomo3 installed and accessible in your system
- CUDA-capable GPU with appropriate drivers installed
- NVIDIA System Management Interface (nvidia-smi) for GPU monitoring
- Additional Python packages for export functionality (mrcfile, tifffile, h5py, pillow)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/Aretomo3_GUI.git
cd Aretomo3_GUI
```

2. Create and activate a virtual environment (recommended):
```bash
python3 -m venv venv
source venv/bin/activate
```

3. Install the required packages:
```bash
pip install -r requirements.txt
```

## Usage

1. Ensure AreTomo3 is properly installed and its path is configured in the GUI.

2. Launch the GUI:
```bash
python main.py
```

3. Configure the processing parameters:
   - Select input directory containing .eer files
   - Choose output directory (or use automatic directory creation)
   - Set processing parameters (pixel size, tilt axis, etc.)
   - Configure GPU and advanced options

4. Process your data:
   - Click "Test Command" to verify the command
   - Click "Run AreTomo3" to start processing
   - Monitor progress in the Progress window
   - View results in the Tomogram Viewer tab

5. **NEW** Batch Processing:
   - Go to the "Batch Processing" tab
   - Add folders or individual tilt series to the batch
   - Configure batch processing options
   - Start batch processing and monitor progress
   - Save batch configurations for future use

6. **NEW** Export Functionality:
   - Navigate to the "Export" tab
   - Select processed tomograms from the dropdown
   - Choose from supported export formats:
     - IMOD MRC format
     - TIFF stack
     - Relion format (with optional STAR file)
     - EMAN2 HDF format
     - ImageJ format (with calibration)
   - Configure format-specific options
   - Export with a single click

## File Structure

The input directory should contain:
- .eer files following the naming pattern: Position_X_NNN_angle_timestamp_EER.eer
- Corresponding .mdoc metadata files

## Monitoring

The GUI provides real-time monitoring of:
- System memory usage
- GPU memory usage
- Processing progress
- File operations

## Export Formats

The Export tab supports conversion to the following scientific formats:

1. **IMOD MRC Format**
   - Standard format for electron microscopy
   - Options for 16-bit integer or 32-bit float data modes
   - Preserves original metadata including pixel size

2. **TIFF Stack**
   - Compatible with most image processing software
   - Options for 8-bit, 16-bit, or 32-bit float depth
   - Multiple compression options (None, LZW, ZIP, JPEG)

3. **Relion Format**
   - MRC format with optional STAR metadata file
   - Suitable for particle picking and subtomogram averaging
   - Preserves essential microscopy metadata

4. **EMAN2 HDF Format**
   - Hierarchical Data Format for EMAN2 software
   - Includes normalization options
   - Stores microscopy metadata in HDF attributes

5. **ImageJ Format**
   - TIFF format optimized for ImageJ/Fiji
   - Includes calibration information
   - Option to create separate calibration file
   - Adjustable Z-spacing for anisotropic voxels

## Tabs Overview

1. **Main Tab**: Set up input/output directories and processing parameters
2. **Queue Tab**: Manage and monitor the processing queue
3. **Monitor Tab**: View system resource usage (CPU, RAM, GPU)
4. **Analysis Tab**: Analyze tilt series data and view results
5. **Viewer Tab**: View MRC files with slice and contrast controls
6. **Batch Tab**: Set up and run batch processing jobs
7. **Export Tab**: Export tomograms to various scientific formats
8. **Log Tab**: View detailed logs of all operations

## Support

For issues and feature requests, please create an issue in the repository.

## Notes

- Make sure you have sufficient disk space for output files
- Monitor system resources during processing
- Keep AreTomo3 path updated in the GUI settings
- When using batch processing with large datasets, consider adjusting the "Max Parallel" option based on your system resources
- For exporting large tomograms, be aware of file size limitations of the target format
- TIFF exports with JPEG compression will be lossy and may not be suitable for quantitative analysis
- Preserving pixel size during export is essential for accurate measurements in other software
