[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "aretomo3-gui"
version = "1.0.0"
description = "Professional GUI for AreTomo3 tomographic reconstruction software"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "AreTomo3 Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "AreTomo3 Team", email = "<EMAIL>"}
]
keywords = [
    "tomography",
    "cryo-electron-microscopy",
    "image-processing",
    "reconstruction",
    "gui",
    "aretomo3"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Topic :: Scientific/Engineering :: Visualization",
]
requires-python = ">=3.8"
dependencies = [
    "PyQt6>=6.4.0",
    "numpy>=1.21.0",
    "matplotlib>=3.5.0",
    "mrcfile>=1.4.0",
    "psutil>=5.8.0",
    "Pillow>=8.3.0",
    "scipy>=1.7.0",
    "h5py>=3.6.0",
    "tifffile>=2021.11.2",
    "imageio>=2.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-qt>=4.2.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=5.0.0",
    "mypy>=0.991",
    "pylint>=2.15.0",
    "pre-commit>=2.20.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "sphinx-autodoc-typehints>=1.19.0",
    "myst-parser>=0.18.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-qt>=4.2.0",
    "pytest-mock>=3.8.0",
]

[project.urls]
Homepage = "https://github.com/aretomo3/aretomo3-gui"
Documentation = "https://aretomo3-gui.readthedocs.io"
Repository = "https://github.com/aretomo3/aretomo3-gui.git"
"Bug Tracker" = "https://github.com/aretomo3/aretomo3-gui/issues"
Changelog = "https://github.com/aretomo3/aretomo3-gui/blob/main/CHANGELOG.md"

[project.scripts]
aretomo3-gui = "aretomo3_gui.main:main"

[project.gui-scripts]
aretomo3-gui = "aretomo3_gui.main:main"

[tool.setuptools]
package-dir = {"" = "."}

[tool.setuptools.packages.find]
where = ["."]
include = ["aretomo3_gui*"]
exclude = ["tests*", "docs*", "examples*"]

[tool.setuptools.package-data]
aretomo3_gui = [
    "gui/themes/*.qss",
    "gui/themes/*.png",
    "utils/*.sh",
    "core/config/*.json",
]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["aretomo3_gui"]
known_third_party = ["PyQt6", "numpy", "matplotlib", "mrcfile", "psutil"]

# MyPy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "mrcfile.*",
    "matplotlib.*",
    "scipy.*",
    "h5py.*",
    "tifffile.*",
    "imageio.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=aretomo3_gui",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gui: marks tests that require GUI",
]

# Coverage configuration
[tool.coverage.run]
source = ["aretomo3_gui"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Pylint configuration
[tool.pylint.messages_control]
disable = [
    "too-many-arguments",
    "too-many-instance-attributes",
    "too-many-locals",
    "too-few-public-methods",
    "import-error",
    "no-name-in-module",
]

[tool.pylint.format]
max-line-length = 88

[tool.pylint.design]
max-args = 10
max-attributes = 15
max-public-methods = 25
