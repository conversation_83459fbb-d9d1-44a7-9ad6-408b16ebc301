import re

def parse_mdoc(file_path):
    """Parse .mdoc file to extract microscope parameters.
    
    Returns:
    dict: Parameters extracted from mdoc file
    """
    params = {
        'pixel_size': None,
        'voltage': None,
        'cs': None,
        'total_dose': 0.0,
        'tilt_axis': None
    }
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
            
        # Extract pixel size (in nm, convert to Angstrom)
        pixel_match = re.search(r'PixelSpacing\s*=\s*([\d.]+)', content)
        if pixel_match:
            params['pixel_size'] = float(pixel_match.group(1)) * 10  # Convert nm to Angstrom
            
        # Extract voltage
        voltage_match = re.search(r'Voltage\s*=\s*([\d.]+)', content)
        if voltage_match:
            params['voltage'] = float(voltage_match.group(1))
            
        # Extract Cs
        cs_match = re.search(r'SphericalAberration\s*=\s*([\d.]+)', content)
        if cs_match:
            params['cs'] = float(cs_match.group(1))
            
        # Extract dose information
        dose_match = re.search(r'Dose\s*=\s*([\d.]+)', content)
        if dose_match:
            params['total_dose'] = float(dose_match.group(1))
            
        # Extract tilt axis
        tilt_match = re.search(r'TiltAxis\s*=\s*([-\d.]+)', content)
        if tilt_match:
            params['tilt_axis'] = float(tilt_match.group(1))
            
    except Exception as e:
        print(f"Error parsing mdoc file: {e}")
        
    return params
