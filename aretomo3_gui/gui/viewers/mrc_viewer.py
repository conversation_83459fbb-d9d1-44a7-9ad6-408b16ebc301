import mrcfile
import numpy as np
import os
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas, NavigationToolbar2QT
from matplotlib.figure import Figure
from matplotlib import colormaps
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, 
                          QSlider, QPushButton, QFileDialog, QGroupBox, QToolBar,
                          QMessageBox, QComboBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QAction
import logging

logger = logging.getLogger(__name__)

class MRCViewer(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.volume = None
        self.current_slice = 0
        self.current_file = None
        self.zoom_factor = 1.0
        self.current_cmap = 'gray'
        
        # Create matplotlib view
        self.view = None
        
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout()
        
        # Add toolbar
        toolbar = QToolBar()
        layout.addWidget(toolbar)
        
        # File operations
        open_action = QAction("Open MRC", self)
        open_action.triggered.connect(self.open_mrc)
        toolbar.addAction(open_action)
        
        save_action = QAction("Save As", self)
        save_action.triggered.connect(self.save_mrc)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # Create matplotlib figure and toolbar
        self.figure = Figure(figsize=(8, 8))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        self.view = self.canvas
        
        # Add matplotlib navigation toolbar
        self.nav_toolbar = NavigationToolbar2QT(self.canvas, self)
        layout.addWidget(self.nav_toolbar)
        layout.addWidget(self.canvas)
        
        # Controls in group boxes
        controls_layout = QHBoxLayout()
        
        # Navigation controls
        nav_group = QGroupBox("Navigation")
        nav_layout = QHBoxLayout()
        
        self.slice_slider = QSlider(Qt.Orientation.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.valueChanged.connect(self.update_slice)
        
        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.setMinimum(0)
        self.slice_spinbox.setMaximum(0)
        self.slice_spinbox.valueChanged.connect(self.update_slice_from_spinbox)
        
        nav_layout.addWidget(QLabel("Slice:"))
        nav_layout.addWidget(self.slice_slider)
        nav_layout.addWidget(self.slice_spinbox)
        nav_group.setLayout(nav_layout)
        controls_layout.addWidget(nav_group)
        
        # Contrast controls
        contrast_group = QGroupBox("Contrast")
        contrast_layout = QHBoxLayout()
        
        self.vmin_spinbox = QSpinBox()
        self.vmin_spinbox.setRange(-32768, 32767)
        self.vmin_spinbox.valueChanged.connect(self.update_display)
        
        self.vmax_spinbox = QSpinBox()
        self.vmax_spinbox.setRange(-32768, 32767)
        self.vmax_spinbox.valueChanged.connect(self.update_display)
        
        self.auto_contrast_btn = QPushButton("Auto")
        self.auto_contrast_btn.clicked.connect(self.auto_contrast)
        
        # Add colormap selector
        self.cmap_combo = QComboBox()
        self.cmap_combo.addItems([
            'gray', 'viridis', 'plasma', 'inferno', 'magma', 
            'hot', 'cool', 'coolwarm', 'RdYlBu', 'jet'
        ])
        self.cmap_combo.setCurrentText('gray')
        self.cmap_combo.currentTextChanged.connect(self.change_colormap)
        
        contrast_layout.addWidget(QLabel("Min:"))
        contrast_layout.addWidget(self.vmin_spinbox)
        contrast_layout.addWidget(QLabel("Max:"))
        contrast_layout.addWidget(self.vmax_spinbox)
        contrast_layout.addWidget(self.auto_contrast_btn)
        contrast_layout.addWidget(QLabel("Colormap:"))
        contrast_layout.addWidget(self.cmap_combo)
        contrast_group.setLayout(contrast_layout)
        controls_layout.addWidget(contrast_group)
        
        # View controls
        view_group = QGroupBox("View")
        view_layout = QHBoxLayout()
        
        self.xy_button = QPushButton("XY View")
        self.xy_button.clicked.connect(lambda: self.change_view('xy'))
        self.xz_button = QPushButton("XZ View")
        self.xz_button.clicked.connect(lambda: self.change_view('xz'))
        self.yz_button = QPushButton("YZ View")
        self.yz_button.clicked.connect(lambda: self.change_view('yz'))
        
        # Add zoom controls
        self.zoom_in_btn = QPushButton("+")
        self.zoom_in_btn.clicked.connect(lambda: self.zoom(1.2))
        self.zoom_out_btn = QPushButton("-")
        self.zoom_out_btn.clicked.connect(lambda: self.zoom(0.8))
        self.zoom_reset_btn = QPushButton("Reset")
        self.zoom_reset_btn.clicked.connect(self.reset_view)
        
        view_layout.addWidget(self.xy_button)
        view_layout.addWidget(self.xz_button)
        view_layout.addWidget(self.yz_button)
        view_layout.addWidget(self.zoom_in_btn)
        view_layout.addWidget(self.zoom_out_btn)
        view_layout.addWidget(self.zoom_reset_btn)
        view_group.setLayout(view_layout)
        controls_layout.addWidget(view_group)
        
        # Add file info label
        self.info_label = QLabel()
        controls_layout.addWidget(self.info_label)
        
        layout.addLayout(controls_layout)
        self.setLayout(layout)
        
        self.current_view = 'xy'
        self.im = None

    def zoom(self, factor):
        """Adjust zoom level"""
        self.zoom_factor *= factor
        self.update_display()
        
    def reset_view(self):
        """Reset zoom and pan"""
        self.zoom_factor = 1.0
        self.ax.set_xlim(auto=True)
        self.ax.set_ylim(auto=True)
        self.update_display()
        
    def change_colormap(self, cmap_name):
        """Change the colormap"""
        self.current_cmap = cmap_name
        self.update_display()
        
    def open_mrc(self):
        """Open an MRC file via file dialog"""
        filepath, _ = QFileDialog.getOpenFileName(
            self,
            "Open MRC File",
            "",
            "MRC Files (*.mrc *.mrcs *.rec);;All Files (*.*)"
        )
        
        if filepath:
            if self.load_mrc(filepath):
                self.current_file = filepath
                self.update_info_label()
                
    def save_mrc(self):
        """Save current volume to a new MRC file"""
        if self.volume is None:
            QMessageBox.warning(self, "No Data", "No volume loaded to save.")
            return
            
        filepath, _ = QFileDialog.getSaveFileName(
            self,
            "Save MRC File",
            "",
            "MRC Files (*.mrc);;All Files (*.*)"
        )
        
        if filepath:
            try:
                with mrcfile.new(filepath, overwrite=True) as mrc:
                    mrc.set_data(self.volume)
                logger.info(f"Successfully saved MRC file to {filepath}")
                QMessageBox.information(self, "Success", "File saved successfully.")
            except Exception as e:
                logger.error(f"Error saving MRC file: {e}")
                QMessageBox.critical(self, "Error", f"Failed to save file:\n{str(e)}")
                
    def load_mrc(self, filepath):
        """Load an MRC file and display its first slice"""
        try:
            with mrcfile.open(filepath, permissive=True) as mrc:
                self.volume = mrc.data.copy()
            
            logger.info(f"Loaded MRC file: {filepath}")
            logger.debug(f"Volume shape: {self.volume.shape}, dtype: {self.volume.dtype}")
            
            self.update_slice_range()
            self.current_slice = 0
            self.slice_slider.setValue(0)
            self.slice_spinbox.setValue(0)
            
            self.auto_contrast()  # Set initial contrast automatically
            self.update_info_label()
            
            return True
        except Exception as e:
            logger.error(f"Error loading MRC file: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load file:\n{str(e)}")
            return False
            
    def auto_contrast(self):
        """Automatically set contrast based on data percentiles"""
        if self.volume is not None:
            vmin = np.percentile(self.volume, 1)
            vmax = np.percentile(self.volume, 99)
            self.vmin_spinbox.setValue(int(vmin))
            self.vmax_spinbox.setValue(int(vmax))
            self.update_display()
            
    def update_info_label(self):
        """Update the information label with current file details"""
        if self.volume is not None:
            info = f"Size: {self.volume.shape} | "
            info += f"Type: {self.volume.dtype} | "
            if self.current_file:
                info += f"File: {os.path.basename(self.current_file)}"
            self.info_label.setText(info)
        else:
            self.info_label.clear()
            
    def update_slice_range(self):
        """Update the range of the slice slider based on volume dimensions"""
        if self.volume is not None:
            max_slice = self.volume.shape[0] - 1 if self.current_view == 'xy' else \
                       self.volume.shape[1] - 1 if self.current_view == 'xz' else \
                       self.volume.shape[2] - 1
            
            self.slice_slider.setMaximum(max_slice)
            self.slice_spinbox.setMaximum(max_slice)
    
    def update_slice(self, value):
        """Update the displayed slice when slider changes"""
        self.current_slice = value
        self.slice_spinbox.setValue(value)
        self.update_display()
        
    def update_slice_from_spinbox(self, value):
        """Update the displayed slice when spinbox changes"""
        self.current_slice = value
        self.slice_slider.setValue(value)
        self.update_display()
        
    def change_view(self, view):
        """Change the viewing direction (xy, xz, or yz)"""
        self.current_view = view
        self.update_slice_range()
        self.current_slice = min(self.current_slice, self.slice_slider.maximum())
        self.slice_slider.setValue(self.current_slice)
        self.update_display()
        
    def update_display(self):
        """Update the displayed image"""
        if self.volume is None:
            return
            
        # Get the appropriate slice based on view direction
        if self.current_view == 'xy':
            slice_data = self.volume[self.current_slice, :, :]
        elif self.current_view == 'xz':
            slice_data = self.volume[:, self.current_slice, :]
        else:  # yz
            slice_data = self.volume[:, :, self.current_slice]
            
        # Clear the current axis
        self.ax.clear()
        
        # Display the new slice with current colormap
        self.im = self.ax.imshow(slice_data, 
                                cmap=self.current_cmap,
                                vmin=self.vmin_spinbox.value(),
                                vmax=self.vmax_spinbox.value())
        
        # Apply zoom
        xmin, xmax = self.ax.get_xlim()
        ymin, ymax = self.ax.get_ylim()
        xcenter = (xmax + xmin) / 2
        ycenter = (ymax + ymin) / 2
        width = (xmax - xmin) / self.zoom_factor
        height = (ymax - ymin) / self.zoom_factor
        self.ax.set_xlim(xcenter - width/2, xcenter + width/2)
        self.ax.set_ylim(ycenter - height/2, ycenter + height/2)
        
        # Update the canvas
        self.canvas.draw()
