import mrcfile
import numpy as np
import os
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas, NavigationToolbar2QT
from matplotlib.figure import Figure
from matplotlib import colormaps
from matplotlib.widgets import RectangleSelector
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox,
                          QSlider, QPushButton, QFileDialog, QGroupBox, QToolBar,
                          QMessageBox, QComboBox, QSplitter, QCheckBox, QDoubleSpinBox,
                          QProgressBar, QTextEdit, QTabWidget, QFrame, QGridLayout,
                          QScrollArea, QButtonGroup, QRadioButton)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QAction, QFont, QPalette, QColor, QPixmap, QPainter
import logging
from pathlib import Path
import time

logger = logging.getLogger(__name__)

class ElegantSlider(QSlider):
    """Custom slider with napari-inspired styling."""

    def __init__(self, orientation=Qt.Orientation.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }

            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 #b4b4b4, stop:1 #8f8f8f);
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }

            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                                          stop:0 #d4d4d4, stop:1 #afafaf);
            }

            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #66e, stop:1 #44c);
                border: 1px solid #777;
                height: 8px;
                border-radius: 4px;
            }
        """)

class HistogramWidget(QWidget):
    """Napari-inspired histogram widget for contrast adjustment."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data = None
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # Create matplotlib figure for histogram
        self.figure = Figure(figsize=(4, 2))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)

        # Style the histogram plot
        self.figure.patch.set_facecolor('none')
        self.ax.set_facecolor('none')

        layout.addWidget(self.canvas)

    def update_histogram(self, data):
        """Update histogram with new data."""
        if data is None:
            return

        self.data = data
        self.ax.clear()

        # Calculate histogram
        hist, bins = np.histogram(data.flatten(), bins=100, density=True)

        # Plot histogram with elegant styling
        self.ax.fill_between(bins[:-1], hist, alpha=0.7, color='#4CAF50')
        self.ax.plot(bins[:-1], hist, color='#2E7D32', linewidth=1.5)

        # Style the plot
        self.ax.set_xlabel('Intensity', fontsize=8)
        self.ax.set_ylabel('Density', fontsize=8)
        self.ax.tick_params(labelsize=7)
        self.ax.grid(True, alpha=0.3)

        # Remove top and right spines
        self.ax.spines['top'].set_visible(False)
        self.ax.spines['right'].set_visible(False)

        self.figure.tight_layout()
        self.canvas.draw()

class MeasurementTool(QWidget):
    """Napari-inspired measurement tools."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.measurements = []
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Tool selection
        tool_group = QGroupBox("Measurement Tools")
        tool_layout = QHBoxLayout(tool_group)

        self.distance_btn = QPushButton("📏 Distance")
        self.angle_btn = QPushButton("📐 Angle")
        self.area_btn = QPushButton("⬜ Area")
        self.clear_btn = QPushButton("🗑️ Clear")

        for btn in [self.distance_btn, self.angle_btn, self.area_btn, self.clear_btn]:
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-weight: 500;
                }
                QPushButton:checked {
                    background-color: #2196F3;
                    color: white;
                }
            """)
            tool_layout.addWidget(btn)

        layout.addWidget(tool_group)

        # Measurements display
        self.measurements_text = QTextEdit()
        self.measurements_text.setMaximumHeight(100)
        self.measurements_text.setPlaceholderText("Measurements will appear here...")
        layout.addWidget(self.measurements_text)

class MRCViewer(QWidget):
    """Enhanced MRC Viewer with napari-inspired features."""

    def __init__(self, parent=None):
        super().__init__(parent)
        # Core data
        self.volume = None
        self.current_slice = 0
        self.current_file = None
        self.current_view = 'xy'
        self.current_cmap = 'gray'

        # Display properties
        self.zoom_factor = 1.0
        self.contrast_limits = [0, 255]
        self.auto_contrast = True

        # Measurement state
        self.measurement_mode = None
        self.measurement_points = []

        # Layer system (napari-inspired)
        self.layers = {}
        self.active_layer = None

        self.setup_ui()

    def setup_ui(self):
        """Set up the elegant napari-inspired UI."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(8)

        # Create elegant toolbar
        self.create_toolbar(main_layout)

        # Create main content area with splitter
        content_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left side - Main viewer
        viewer_widget = self.create_viewer_widget()
        content_splitter.addWidget(viewer_widget)

        # Right side - Control panel
        control_panel = self.create_control_panel()
        content_splitter.addWidget(control_panel)

        # Set splitter proportions (80% viewer, 20% controls)
        content_splitter.setStretchFactor(0, 8)
        content_splitter.setStretchFactor(1, 2)
        content_splitter.setSizes([800, 200])

        main_layout.addWidget(content_splitter)

        # Initialize state
        self.im = None

    def create_toolbar(self, layout):
        """Create elegant toolbar with napari-inspired styling."""
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 4px;
                spacing: 8px;
            }
            QToolBar::separator {
                background: #adb5bd;
                width: 1px;
                margin: 4px 8px;
            }
        """)

        # File operations with icons
        open_action = QAction("📁 Open MRC", self)
        open_action.setToolTip("Open MRC file (Ctrl+O)")
        open_action.triggered.connect(self.open_mrc)
        toolbar.addAction(open_action)

        save_action = QAction("💾 Save As", self)
        save_action.setToolTip("Save current view")
        save_action.triggered.connect(self.save_mrc)
        toolbar.addAction(save_action)

        toolbar.addSeparator()

        # View controls
        reset_action = QAction("🔄 Reset View", self)
        reset_action.setToolTip("Reset zoom and pan")
        reset_action.triggered.connect(self.reset_view)
        toolbar.addAction(reset_action)

        auto_contrast_action = QAction("🎨 Auto Contrast", self)
        auto_contrast_action.setToolTip("Auto-adjust contrast")
        auto_contrast_action.triggered.connect(self.auto_contrast)
        toolbar.addAction(auto_contrast_action)

        layout.addWidget(toolbar)

    def create_viewer_widget(self):
        """Create the main viewer widget."""
        viewer_widget = QWidget()
        viewer_layout = QVBoxLayout(viewer_widget)
        viewer_layout.setContentsMargins(0, 0, 0, 0)

        # Create matplotlib figure with elegant styling
        self.figure = Figure(figsize=(10, 8))
        self.figure.patch.set_facecolor('#ffffff')

        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)

        # Style the axes
        self.ax.set_facecolor('#f8f9fa')
        for spine in self.ax.spines.values():
            spine.set_color('#dee2e6')
            spine.set_linewidth(1)

        # Add custom navigation toolbar
        self.nav_toolbar = NavigationToolbar2QT(self.canvas, self)
        self.nav_toolbar.setStyleSheet("""
            QToolBar {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 2px;
            }
        """)

        viewer_layout.addWidget(self.nav_toolbar)
        viewer_layout.addWidget(self.canvas)

        # Add slice navigation at bottom
        slice_nav = self.create_slice_navigation()
        viewer_layout.addWidget(slice_nav)

        return viewer_widget

    def create_slice_navigation(self):
        """Create elegant slice navigation controls."""
        nav_widget = QWidget()
        nav_layout = QHBoxLayout(nav_widget)
        nav_layout.setContentsMargins(10, 5, 10, 5)

        # Slice label
        nav_layout.addWidget(QLabel("Slice:"))

        # Elegant slice slider
        self.slice_slider = ElegantSlider(Qt.Orientation.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.valueChanged.connect(self.update_slice)
        nav_layout.addWidget(self.slice_slider)

        # Slice spinbox
        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.setMinimum(0)
        self.slice_spinbox.setMaximum(0)
        self.slice_spinbox.valueChanged.connect(self.update_slice_from_spinbox)
        self.slice_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 4px 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                min-width: 60px;
            }
        """)
        nav_layout.addWidget(self.slice_spinbox)

        # Total slices label
        self.total_slices_label = QLabel("/ 0")
        self.total_slices_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        nav_layout.addWidget(self.total_slices_label)

        return nav_widget

    def create_control_panel(self):
        """Create the right-side control panel."""
        panel = QWidget()
        panel_layout = QVBoxLayout(panel)
        panel_layout.setContentsMargins(5, 5, 5, 5)
        panel_layout.setSpacing(10)

        # Create tabbed control panel
        control_tabs = QTabWidget()
        control_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background: #ffffff;
            }
            QTabBar::tab {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 6px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: #ffffff;
                border-bottom: none;
            }
        """)

        # View Controls Tab
        view_tab = self.create_view_controls()
        control_tabs.addTab(view_tab, "🔍 View")

        # Contrast Tab
        contrast_tab = self.create_contrast_controls()
        control_tabs.addTab(contrast_tab, "🎨 Contrast")

        # Measurements Tab
        measurement_tab = MeasurementTool()
        control_tabs.addTab(measurement_tab, "📏 Measure")

        # Info Tab
        info_tab = self.create_info_panel()
        control_tabs.addTab(info_tab, "ℹ️ Info")

        panel_layout.addWidget(control_tabs)

        return panel

    def create_view_controls(self):
        """Create view control widgets."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(12)

        # View direction controls
        view_group = QGroupBox("View Direction")
        view_layout = QGridLayout(view_group)

        self.xy_button = QPushButton("XY View")
        self.xz_button = QPushButton("XZ View")
        self.yz_button = QPushButton("YZ View")

        for i, btn in enumerate([self.xy_button, self.xz_button, self.yz_button]):
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-weight: 500;
                    min-height: 30px;
                }
                QPushButton:checked {
                    background-color: #2196F3;
                    color: white;
                }
            """)
            view_layout.addWidget(btn, i // 2, i % 2)

        self.xy_button.setChecked(True)
        self.xy_button.clicked.connect(lambda: self.change_view('xy'))
        self.xz_button.clicked.connect(lambda: self.change_view('xz'))
        self.yz_button.clicked.connect(lambda: self.change_view('yz'))

        layout.addWidget(view_group)

        # Zoom controls
        zoom_group = QGroupBox("Zoom")
        zoom_layout = QGridLayout(zoom_group)

        self.zoom_in_btn = QPushButton("🔍+ Zoom In")
        self.zoom_out_btn = QPushButton("🔍- Zoom Out")
        self.zoom_reset_btn = QPushButton("🔄 Reset")

        for btn in [self.zoom_in_btn, self.zoom_out_btn, self.zoom_reset_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 11px;
                }
            """)

        self.zoom_in_btn.clicked.connect(lambda: self.zoom(1.2))
        self.zoom_out_btn.clicked.connect(lambda: self.zoom(0.8))
        self.zoom_reset_btn.clicked.connect(self.reset_view)

        zoom_layout.addWidget(self.zoom_in_btn, 0, 0)
        zoom_layout.addWidget(self.zoom_out_btn, 0, 1)
        zoom_layout.addWidget(self.zoom_reset_btn, 1, 0, 1, 2)

        layout.addWidget(zoom_group)
        layout.addStretch()

        return widget

    def create_contrast_controls(self):
        """Create contrast control widgets."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(12)

        # Contrast adjustment
        contrast_group = QGroupBox("Contrast Limits")
        contrast_layout = QGridLayout(contrast_group)

        # Min/Max spinboxes
        self.vmin_spinbox = QSpinBox()
        self.vmin_spinbox.setRange(-32768, 32767)
        self.vmin_spinbox.valueChanged.connect(self.update_display)

        self.vmax_spinbox = QSpinBox()
        self.vmax_spinbox.setRange(-32768, 32767)
        self.vmax_spinbox.valueChanged.connect(self.update_display)

        contrast_layout.addWidget(QLabel("Min:"), 0, 0)
        contrast_layout.addWidget(self.vmin_spinbox, 0, 1)
        contrast_layout.addWidget(QLabel("Max:"), 1, 0)
        contrast_layout.addWidget(self.vmax_spinbox, 1, 1)

        # Auto contrast button
        self.auto_contrast_btn = QPushButton("🎨 Auto Contrast")
        self.auto_contrast_btn.clicked.connect(self.auto_contrast)
        self.auto_contrast_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: 500;
                background-color: #4CAF50;
                color: white;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        contrast_layout.addWidget(self.auto_contrast_btn, 2, 0, 1, 2)

        layout.addWidget(contrast_group)

        # Colormap selection
        cmap_group = QGroupBox("Colormap")
        cmap_layout = QVBoxLayout(cmap_group)

        self.cmap_combo = QComboBox()
        self.cmap_combo.addItems([
            'gray', 'viridis', 'plasma', 'inferno', 'magma',
            'hot', 'cool', 'coolwarm', 'RdYlBu', 'jet'
        ])
        self.cmap_combo.setCurrentText('gray')
        self.cmap_combo.currentTextChanged.connect(self.change_colormap)
        self.cmap_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
            }
        """)
        cmap_layout.addWidget(self.cmap_combo)

        layout.addWidget(cmap_group)

        # Histogram
        histogram_group = QGroupBox("Histogram")
        histogram_layout = QVBoxLayout(histogram_group)

        self.histogram_widget = HistogramWidget()
        histogram_layout.addWidget(self.histogram_widget)

        layout.addWidget(histogram_group)
        layout.addStretch()

        return widget

    def create_info_panel(self):
        """Create information panel."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)

        # File info
        info_group = QGroupBox("File Information")
        info_layout = QVBoxLayout(info_group)

        self.info_label = QLabel("No file loaded")
        self.info_label.setWordWrap(True)
        self.info_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: monospace;
                font-size: 11px;
            }
        """)
        info_layout.addWidget(self.info_label)

        layout.addWidget(info_group)

        # Statistics
        stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout(stats_group)

        self.stats_label = QLabel("No data")
        self.stats_label.setWordWrap(True)
        self.stats_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: monospace;
                font-size: 11px;
            }
        """)
        stats_layout.addWidget(self.stats_label)

        layout.addWidget(stats_group)
        layout.addStretch()

        return widget

    def zoom(self, factor):
        """Adjust zoom level"""
        self.zoom_factor *= factor
        self.update_display()

    def reset_view(self):
        """Reset zoom and pan"""
        self.zoom_factor = 1.0
        self.ax.set_xlim(auto=True)
        self.ax.set_ylim(auto=True)
        self.update_display()

    def change_colormap(self, cmap_name):
        """Change the colormap"""
        self.current_cmap = cmap_name
        self.update_display()

    def open_mrc(self):
        """Open an MRC file via file dialog"""
        filepath, _ = QFileDialog.getOpenFileName(
            self,
            "Open MRC File",
            "",
            "MRC Files (*.mrc *.mrcs *.rec);;All Files (*.*)"
        )

        if filepath:
            if self.load_mrc(filepath):
                self.current_file = filepath
                self.update_info_label()

    def save_mrc(self):
        """Save current volume to a new MRC file"""
        if self.volume is None:
            QMessageBox.warning(self, "No Data", "No volume loaded to save.")
            return

        filepath, _ = QFileDialog.getSaveFileName(
            self,
            "Save MRC File",
            "",
            "MRC Files (*.mrc);;All Files (*.*)"
        )

        if filepath:
            try:
                with mrcfile.new(filepath, overwrite=True) as mrc:
                    mrc.set_data(self.volume)
                logger.info(f"Successfully saved MRC file to {filepath}")
                QMessageBox.information(self, "Success", "File saved successfully.")
            except Exception as e:
                logger.error(f"Error saving MRC file: {e}")
                QMessageBox.critical(self, "Error", f"Failed to save file:\n{str(e)}")

    def load_mrc(self, filepath):
        """Load an MRC file and display its first slice"""
        try:
            with mrcfile.open(filepath, permissive=True) as mrc:
                self.volume = mrc.data.copy()

            logger.info(f"Loaded MRC file: {filepath}")
            logger.debug(f"Volume shape: {self.volume.shape}, dtype: {self.volume.dtype}")

            self.update_slice_range()
            self.current_slice = 0
            self.slice_slider.setValue(0)
            self.slice_spinbox.setValue(0)

            self.auto_contrast()  # Set initial contrast automatically
            self.update_info_label()

            return True
        except Exception as e:
            logger.error(f"Error loading MRC file: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load file:\n{str(e)}")
            return False

    def auto_contrast(self):
        """Automatically set contrast based on data percentiles"""
        if self.volume is not None:
            vmin = np.percentile(self.volume, 1)
            vmax = np.percentile(self.volume, 99)
            self.vmin_spinbox.setValue(int(vmin))
            self.vmax_spinbox.setValue(int(vmax))
            self.update_display()

    def update_info_label(self):
        """Update the information labels with current file details"""
        if self.volume is not None:
            # File information
            file_info = f"📁 File: {os.path.basename(self.current_file) if self.current_file else 'Unknown'}\n"
            file_info += f"📐 Dimensions: {self.volume.shape}\n"
            file_info += f"🔢 Data Type: {self.volume.dtype}\n"
            file_info += f"💾 Size: {self.volume.nbytes / (1024**2):.1f} MB\n"
            file_info += f"👁️ Current View: {self.current_view.upper()}\n"
            file_info += f"🎯 Slice: {self.current_slice + 1} / {self.slice_slider.maximum() + 1}"

            self.info_label.setText(file_info)

            # Statistics
            current_slice_data = self.get_current_slice_data()
            if current_slice_data is not None:
                stats_info = f"📊 Current Slice Statistics:\n"
                stats_info += f"Min: {np.min(current_slice_data):.2f}\n"
                stats_info += f"Max: {np.max(current_slice_data):.2f}\n"
                stats_info += f"Mean: {np.mean(current_slice_data):.2f}\n"
                stats_info += f"Std: {np.std(current_slice_data):.2f}\n"
                stats_info += f"Median: {np.median(current_slice_data):.2f}"

                self.stats_label.setText(stats_info)

                # Update histogram
                self.histogram_widget.update_histogram(current_slice_data)
        else:
            self.info_label.setText("No file loaded")
            self.stats_label.setText("No data")

    def get_current_slice_data(self):
        """Get the current slice data based on view direction."""
        if self.volume is None:
            return None

        if self.current_view == 'xy':
            return self.volume[self.current_slice, :, :]
        elif self.current_view == 'xz':
            return self.volume[:, self.current_slice, :]
        else:  # yz
            return self.volume[:, :, self.current_slice]

    def update_slice_range(self):
        """Update the range of the slice slider based on volume dimensions"""
        if self.volume is not None:
            max_slice = self.volume.shape[0] - 1 if self.current_view == 'xy' else \
                       self.volume.shape[1] - 1 if self.current_view == 'xz' else \
                       self.volume.shape[2] - 1

            self.slice_slider.setMaximum(max_slice)
            self.slice_spinbox.setMaximum(max_slice)

            # Update total slices label
            self.total_slices_label.setText(f"/ {max_slice + 1}")

            # Update info display
            self.update_info_label()

    def update_slice(self, value):
        """Update the displayed slice when slider changes"""
        self.current_slice = value
        self.slice_spinbox.setValue(value)
        self.update_display()

    def update_slice_from_spinbox(self, value):
        """Update the displayed slice when spinbox changes"""
        self.current_slice = value
        self.slice_slider.setValue(value)
        self.update_display()

    def change_view(self, view):
        """Change the viewing direction (xy, xz, or yz)"""
        self.current_view = view

        # Update button states
        self.xy_button.setChecked(view == 'xy')
        self.xz_button.setChecked(view == 'xz')
        self.yz_button.setChecked(view == 'yz')

        self.update_slice_range()
        self.current_slice = min(self.current_slice, self.slice_slider.maximum())
        self.slice_slider.setValue(self.current_slice)
        self.update_display()

    def update_display(self):
        """Update the displayed image with elegant styling."""
        if self.volume is None:
            return

        # Get the appropriate slice based on view direction
        slice_data = self.get_current_slice_data()
        if slice_data is None:
            return

        # Clear the current axis
        self.ax.clear()

        # Display the new slice with current colormap
        self.im = self.ax.imshow(slice_data,
                                cmap=self.current_cmap,
                                vmin=self.vmin_spinbox.value(),
                                vmax=self.vmax_spinbox.value(),
                                interpolation='nearest',
                                aspect='equal')

        # Style the axes with elegant appearance
        self.ax.set_title(f"{self.current_view.upper()} View - Slice {self.current_slice + 1}",
                         fontsize=12, fontweight='bold', pad=10)

        # Add scale information
        if hasattr(self, 'pixel_size'):
            self.ax.set_xlabel(f"X (pixels)", fontsize=10)
            self.ax.set_ylabel(f"Y (pixels)", fontsize=10)
        else:
            self.ax.set_xlabel("X", fontsize=10)
            self.ax.set_ylabel("Y", fontsize=10)

        # Apply zoom with smooth transitions
        if hasattr(self, 'zoom_factor'):
            xmin, xmax = self.ax.get_xlim()
            ymin, ymax = self.ax.get_ylim()
            xcenter = (xmax + xmin) / 2
            ycenter = (ymax + ymin) / 2
            width = (xmax - xmin) / self.zoom_factor
            height = (ymax - ymin) / self.zoom_factor
            self.ax.set_xlim(xcenter - width/2, xcenter + width/2)
            self.ax.set_ylim(ycenter - height/2, ycenter + height/2)

        # Style the plot
        self.ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)

        # Update the canvas
        self.canvas.draw()

        # Update information panels
        self.update_info_label()
