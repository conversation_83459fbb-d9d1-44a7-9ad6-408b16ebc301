#!/usr/bin/env python3

import os
import json
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                           QTableWidgetItem, QPushButton, QLabel, QFileDialog,
                           QHeaderView, QCheckBox, QMessageBox, QSpinBox,
                           QGroupBox, QComboBox)
from PyQt6.QtCore import Qt, pyqtSignal

class BatchProcessingWidget(QWidget):
    """Widget for managing batch processing of multiple tilt series"""
    start_batch = pyqtSignal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent  # Store direct reference to main window
        print(f"DEBUG: BatchProcessingWidget created with parent class: {type(parent).__name__}")
        
        self.setup_ui()
        self.batch_items = []
        
        # Verify parent has the expected methods
        if parent:
            # Try to find the parent window with the find_tilt_series method
            has_find_method = hasattr(parent, 'find_tilt_series') and callable(getattr(parent, 'find_tilt_series'))
            has_log_method = hasattr(parent, 'log_message') and callable(getattr(parent, 'log_message'))
            
            if not has_find_method:
                print(f"WARNING: Parent does not have a callable find_tilt_series method")
                # Print parent class for debugging
                print(f"Parent class: {type(parent).__name__}")
            
            if not has_log_method:
                print(f"WARNING: Parent does not have a callable log_message method")
    
    def safe_log(self, message):
        """Safely log a message using the parent's log_message method if available"""
        # Try to log through the main window reference
        if hasattr(self.main_window, 'log_message') and callable(getattr(self.main_window, 'log_message')):
            self.main_window.log_message(message)
        else:
            print(f"[Batch Processing] {message}")  # Fallback to console output
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # Batch control buttons
        control_group = QGroupBox("Batch Controls")
        control_layout = QHBoxLayout()
        
        self.add_folder_btn = QPushButton("Add Folder")
        self.add_folder_btn.clicked.connect(self.add_folder)
        
        self.add_series_btn = QPushButton("Add Selected Series")
        self.add_series_btn.clicked.connect(self.add_selected_series)
        
        self.remove_btn = QPushButton("Remove Selected")
        self.remove_btn.clicked.connect(self.remove_selected)
        
        self.clear_btn = QPushButton("Clear All")
        self.clear_btn.clicked.connect(self.clear_batch)
        
        self.save_batch_btn = QPushButton("Save Batch")
        self.save_batch_btn.clicked.connect(self.save_batch)
        
        self.load_batch_btn = QPushButton("Load Batch")
        self.load_batch_btn.clicked.connect(self.load_batch)
        
        control_layout.addWidget(self.add_folder_btn)
        control_layout.addWidget(self.add_series_btn)
        control_layout.addWidget(self.remove_btn)
        control_layout.addWidget(self.clear_btn)
        control_layout.addWidget(self.save_batch_btn)
        control_layout.addWidget(self.load_batch_btn)
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)
        
        # Batch processing options
        options_group = QGroupBox("Batch Processing Options")
        options_layout = QHBoxLayout()
        
        self.output_dir_override_chk = QCheckBox("Override Output Directory")
        
        self.gpu_select = QComboBox()
        self.gpu_select.addItems(["Auto", "GPU 0", "GPU 1", "GPU 2", "GPU 3"])
        
        self.max_parallel = QSpinBox()
        self.max_parallel.setRange(1, 8)
        self.max_parallel.setValue(1)
        self.max_parallel.setToolTip("Maximum number of parallel processes")
        
        options_layout.addWidget(self.output_dir_override_chk)
        options_layout.addWidget(QLabel("GPU:"))
        options_layout.addWidget(self.gpu_select)
        options_layout.addWidget(QLabel("Max Parallel:"))
        options_layout.addWidget(self.max_parallel)
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # Batch table
        self.batch_table = QTableWidget(0, 4)
        self.batch_table.setHorizontalHeaderLabels(["Position", "Input Directory", "File Count", "Status"])
        self.batch_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.batch_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.batch_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        layout.addWidget(self.batch_table)
        
        # Execute buttons
        execute_layout = QHBoxLayout()
        self.start_batch_btn = QPushButton("Start Batch Processing")
        self.start_batch_btn.clicked.connect(self.start_processing)
        self.stop_batch_btn = QPushButton("Stop Batch Processing")
        self.stop_batch_btn.clicked.connect(self.stop_processing)
        self.stop_batch_btn.setEnabled(False)
        
        execute_layout.addWidget(self.start_batch_btn)
        execute_layout.addWidget(self.stop_batch_btn)
        layout.addLayout(execute_layout)
    
    def add_folder(self):
        """Add all tilt series from a folder to the batch"""
        try:
            folder = QFileDialog.getExistingDirectory(self, "Select Directory with Tilt Series")
            if not folder:
                return
                
            # Look for tilt series in the folder (delegate to main window)
            if hasattr(self.main_window, 'find_tilt_series') and callable(getattr(self.main_window, 'find_tilt_series')):
                self.safe_log(f"Looking for tilt series in {folder}")
                series_found = self.main_window.find_tilt_series(folder)
                
                if series_found and len(series_found) > 0:
                    count = 0
                    for pos_name, series in series_found.items():
                        if self.add_series_to_batch(series, folder):
                            count += 1
                    
                    QMessageBox.information(self, "Tilt Series Added", 
                                         f"Added {count} tilt series from {folder}")
                    return count
                else:
                    QMessageBox.warning(self, "No Tilt Series Found", 
                                      f"No valid tilt series found in {folder}")
            else:
                error_msg = "Unable to find tilt series - function not available"
                self.safe_log(f"ERROR: {error_msg}")
                if hasattr(self.main_window, 'find_tilt_series'):
                    self.safe_log(f"find_tilt_series exists but is not callable")
                self.safe_log("Main window methods: " + 
                       str([m for m in dir(self.main_window) if not m.startswith('_') and callable(getattr(self.main_window, m))]))
                QMessageBox.warning(self, "Function Not Available", error_msg)
        except Exception as e:
            error_msg = f"Error adding folder: {str(e)}"
            print(f"ERROR: {error_msg}")  # Always print to console
            QMessageBox.critical(self, "Error", error_msg)
            import traceback
            print(traceback.format_exc())  # Print stack trace to console
    
    def add_selected_series(self):
        """Add the currently selected tilt series to the batch"""
        try:
            if hasattr(self.main_window, 'current_position') and self.main_window.current_position:
                series = self.main_window.current_position
                input_dir = self.main_window.input_dir.text() if hasattr(self.main_window, 'input_dir') else ""
                
                if series:
                    self.safe_log(f"Adding selected series {series.position_name} to batch")
                    if self.add_series_to_batch(series, input_dir):
                        QMessageBox.information(self, "Series Added", 
                                             f"Added {series.position_name} to batch processing")
                    else:
                        QMessageBox.information(self, "Series Already Added", 
                                             f"{series.position_name} is already in the batch")
                else:
                    self.safe_log("No series data available for selected position")
                    QMessageBox.warning(self, "No Series Selected", 
                                      "No tilt series is currently selected")
            else:
                QMessageBox.warning(self, "No Series Selected", 
                                  "No tilt series is currently selected")
        except Exception as e:
            error_msg = f"Error adding selected series: {str(e)}"
            print(f"ERROR: {error_msg}")  # Always print to console
            QMessageBox.critical(self, "Error", error_msg)
            import traceback
            print(traceback.format_exc())  # Print stack trace to console
    
    def add_series_to_batch(self, series, input_dir):
        """Add a single tilt series to the batch table"""
        # Check if already in batch
        for item in self.batch_items:
            if item['position'] == series.position_name and item['input_dir'] == input_dir:
                return False  # Already in batch
                
        # Add to batch items list
        batch_item = {
            'position': series.position_name,
            'input_dir': input_dir,
            'file_count': len(series.files),
            'status': 'Pending',
            'series': series
        }
        self.batch_items.append(batch_item)
        
        # Add to table
        row = self.batch_table.rowCount()
        self.batch_table.insertRow(row)
        self.batch_table.setItem(row, 0, QTableWidgetItem(series.position_name))
        self.batch_table.setItem(row, 1, QTableWidgetItem(input_dir))
        self.batch_table.setItem(row, 2, QTableWidgetItem(str(len(series.files))))
        self.batch_table.setItem(row, 3, QTableWidgetItem('Pending'))
        
        return True
    
    def remove_selected(self):
        """Remove selected items from the batch"""
        selected_rows = sorted(set(index.row() for index in self.batch_table.selectedIndexes()), reverse=True)
        
        for row in selected_rows:
            if 0 <= row < len(self.batch_items):
                self.batch_items.pop(row)
                self.batch_table.removeRow(row)
    
    def clear_batch(self):
        """Clear all items from the batch"""
        self.batch_items.clear()
        self.batch_table.setRowCount(0)
    
    def save_batch(self):
        """Save current batch to a JSON file"""
        if not self.batch_items:
            QMessageBox.warning(self, "Empty Batch", "No items in batch to save")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Batch", "", "JSON Files (*.json)")
        if not file_path:
            return
            
        # Create serializable data
        save_data = []
        for item in self.batch_items:
            save_item = {
                'position': item['position'],
                'input_dir': item['input_dir'],
                'file_count': item['file_count'],
                'status': item['status']
            }
            save_data.append(save_item)
            
        try:
            with open(file_path, 'w') as f:
                json.dump(save_data, f, indent=2)
            QMessageBox.information(self, "Batch Saved", f"Batch saved to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error saving batch: {str(e)}")
    
    def load_batch(self):
        """Load batch from a JSON file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Load Batch", "", "JSON Files (*.json)")
        if not file_path:
            return
            
        try:
            with open(file_path, 'r') as f:
                save_data = json.load(f)
                
            # Clear current batch
            self.clear_batch()
            
            self.safe_log(f"Loading batch from {file_path} with {len(save_data)} items")
            
            # Add items from file
            loaded_count = 0
            not_found_count = 0
            
            for item in save_data:
                input_dir = item['input_dir']
                
                # Try to locate the series in the input directory
                found = False
                if hasattr(self.main_window, 'find_tilt_series') and os.path.exists(input_dir):
                    self.safe_log(f"Searching for tilt series at {input_dir}")
                    series_dict = self.main_window.find_tilt_series(input_dir)
                    if series_dict and item['position'] in series_dict:
                        series = series_dict[item['position']]
                        if self.add_series_to_batch(series, input_dir):
                            self.safe_log(f"Loaded series {item['position']} from {input_dir}")
                            loaded_count += 1
                            found = True
                
                if not found:
                    self.safe_log(f"Could not find series {item['position']} at {input_dir}")
                    not_found_count += 1
                    # Add as placeholder with warning
                    row = self.batch_table.rowCount()
                    self.batch_table.insertRow(row)
                    self.batch_table.setItem(row, 0, QTableWidgetItem(item['position']))
                    self.batch_table.setItem(row, 1, QTableWidgetItem(input_dir))
                    self.batch_table.setItem(row, 2, QTableWidgetItem(str(item['file_count'])))
                    self.batch_table.setItem(row, 3, QTableWidgetItem('Not Found'))
                    
                    # Add placeholder to items
                    self.batch_items.append({
                        'position': item['position'],
                        'input_dir': input_dir,
                        'file_count': item['file_count'],
                        'status': 'Not Found',
                        'series': None
                    })
            
            message = f"Loaded batch from {file_path}: {loaded_count} series loaded, {not_found_count} not found"
            self.safe_log(message)
            QMessageBox.information(self, "Batch Loaded", message)
            
        except Exception as e:
            error_msg = f"Error loading batch: {str(e)}"
            self.safe_log(f"ERROR: {error_msg}")
            QMessageBox.critical(self, "Error", error_msg)
            import traceback
            print(traceback.format_exc())  # Print stack trace to console
    
    def start_processing(self):
        """Start batch processing"""
        if not self.batch_items:
            QMessageBox.warning(self, "Empty Batch", "No items in batch to process")
            return
            
        # Check for items with missing series
        missing_series = [item for item in self.batch_items if item['status'] == 'Not Found']
        if missing_series:
            result = QMessageBox.question(self, "Missing Series", 
                                       f"There are {len(missing_series)} series that can't be found. Continue anyway?",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            if result == QMessageBox.StandardButton.No:
                return
        
        # Log start of batch processing
        self.safe_log(f"Starting batch processing of {len(self.batch_items)} items")
                
        # Emit signal to start batch processing
        self.start_batch.emit(self.batch_items)
        
        # Update UI
        self.start_batch_btn.setEnabled(False)
        self.stop_batch_btn.setEnabled(True)
    
    def stop_processing(self):
        """Stop batch processing"""
        self.safe_log("Stopping batch processing")
        
        if hasattr(self.parent(), 'stop_batch_processing'):
            self.parent().stop_batch_processing()
        else:
            self.safe_log("WARNING: Parent does not have stop_batch_processing method")
        
        # Update UI
        self.start_batch_btn.setEnabled(True)
        self.stop_batch_btn.setEnabled(False)
    
    def update_item_status(self, position, status):
        """Update status of batch item"""
        for i, item in enumerate(self.batch_items):
            if item['position'] == position:
                item['status'] = status
                self.batch_table.item(i, 3).setText(status)
                self.safe_log(f"Updated {position} status to: {status}")
                return True
        
        self.safe_log(f"WARNING: Could not find batch item with position {position} to update status")
        return False

    def prepare_tilt_series(self, series, input_dir, output_dir):
        """Prepare tilt series for processing, handling both filename formats"""
        try:
            import sys
            
            # Check if any filename uses bracket notation
            has_bracket_format = False
            for file in series.files:
                if '[' in os.path.basename(file) and ']' in os.path.basename(file):
                    has_bracket_format = True
                    break
            
            if has_bracket_format:
                self.safe_log(f"Series {series.position_name} has bracket notation in filenames, updating mdoc files")
                
                # Import the mdoc update utility module
                try:
                    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
                    import update_subframe_paths
                    
                    # Process the current folder to update mdoc files
                    update_subframe_paths.process_folder(input_dir)
                    self.safe_log(f"Updated mdoc files in {input_dir}")
                    
                    # Create symbolic links if needed
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir, exist_ok=True)
                    
                    # Log success
                    self.safe_log(f"Successfully prepared tilt series with bracket notation")
                    return True
                    
                except ImportError as e:
                    self.safe_log(f"ERROR: Failed to import update_subframe_paths module: {str(e)}")
                    return False
                except Exception as e:
                    self.safe_log(f"ERROR: Failed to prepare tilt series: {str(e)}")
                    return False
            else:
                # No special handling needed for standard format
                self.safe_log(f"Series {series.position_name} uses standard filename format, no preparation needed")
                return True
                
        except Exception as e:
            self.safe_log(f"ERROR: Failed to prepare tilt series: {str(e)}")
            return False
