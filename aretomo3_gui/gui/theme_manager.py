"""Theme manager for the AreTomo3 GUI."""

from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QApplication
from pathlib import Path
import json
import os

class ThemeManager(QObject):
    """Manages application themes."""
    
    theme_changed = pyqtSignal(str)  # Signal emitted when theme changes
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.themes_dir = Path(__file__).parent / "themes"
        self.themes_dir.mkdir(exist_ok=True)
        self._load_theme_settings()
        
    def _load_theme_settings(self):
        """Load saved theme settings."""
        settings_path = self.themes_dir / "theme_settings.json"
        if settings_path.exists():
            try:
                with open(settings_path) as f:
                    settings = json.load(f)
                    self.current_theme = settings.get("current_theme", "light")
            except Exception as e:
                print(f"Error loading theme settings: {e}")
                
    def _save_theme_settings(self):
        """Save current theme settings."""
        settings_path = self.themes_dir / "theme_settings.json"
        try:
            with open(settings_path, 'w') as f:
                json.dump({"current_theme": self.current_theme}, f)
        except Exception as e:
            print(f"Error saving theme settings: {e}")
            
    def get_theme_stylesheet(self):
        """Get the stylesheet for the current theme."""
        # Common styles for both themes
        common_style = """
            /* Global Styles */
            * {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
            }
            
            /* Main Window */
            QMainWindow, QDialog {
                margin: 0;
                spacing: 8px;
            }
            
            /* Group Box */
            QGroupBox {
                font-weight: 500;
                border: 2px solid;
                border-radius: 8px;
                margin-top: 1.2em;
                padding: 12px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
            }
            
            /* Buttons */
            QPushButton {
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: 500;
                min-width: 80px;
                font-size: 13px;
            }
            
            QPushButton:hover {
                transform: translateY(-1px);
            }
            
            QPushButton:pressed {
                transform: translateY(1px);
            }
            
            /* Input Fields */
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                padding: 8px;
                border-radius: 6px;
                min-height: 28px;
                font-size: 13px;
            }
            
            QSpinBox, QDoubleSpinBox {
                padding-right: 15px;
                min-width: 90px;
            }
            
            /* Labels */
            QLabel {
                font-size: 13px;
                font-weight: 400;
                padding: 2px 4px;
            }
            
            /* Containers */
            QScrollArea, QTabWidget::pane {
                border: 2px solid;
                border-radius: 8px;
            }
            """
        
        if self.current_theme == "dark":
            return common_style + """
            /* Dark theme */
            QMainWindow, QDialog {
                background-color: #1a1a1a;
                color: #ffffff;
            }
            
            QWidget {
                background-color: #1a1a1a;
                color: #ffffff;
            }
            
            QGroupBox {
                border-color: #383838;
                background-color: #242424;
            }
            
            QGroupBox::title {
                color: #ffffff;
                background-color: #1a1a1a;
            }
            
            /* Elegant gradient buttons */
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #454545, stop:1 #383838);
                border: 1px solid #505050;
                color: #ffffff;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #505050, stop:1 #454545);
                border-color: #606060;
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #353535, stop:1 #404040);
            }
            
            /* Input fields */
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                background-color: #242424;
                border: 1px solid #383838;
                color: #ffffff;
            }
            
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border-color: #4d4d4d;
                background-color: #2a2a2a;
            }
            
            /* Spinbox styling */
            QSpinBox, QDoubleSpinBox {
                min-width: 90px;
                padding-right: 20px;
            }
            
            QSpinBox::up-button, QDoubleSpinBox::up-button,
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                width: 20px;
                border-radius: 4px;
                border: 1px solid #383838;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #454545, stop:1 #383838);
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #505050, stop:1 #454545);
                border-color: #606060;
            }
            
            QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed,
            QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #353535, stop:1 #404040);
            }
            
            QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-bottom: 6px solid #ffffff;
            }
            
            QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #ffffff;
            }
            
            /* Tab styling */
            QTabWidget::pane {
                border: 1px solid #383838;
                background-color: #242424;
            }
            
            QTabBar::tab {
                background-color: #2a2a2a;
                color: #b0b0b0;
                border: 1px solid #383838;
                padding: 8px 16px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            
            QTabBar::tab:selected {
                background-color: #333333;
                color: #ffffff;
                border-bottom-color: #333333;
            }
            
            QTabBar::tab:hover:!selected {
                background-color: #2d2d2d;
                color: #ffffff;
            }
            
            /* Scrollbars */
            QScrollBar:vertical {
                background-color: #242424;
                width: 14px;
                margin: 0;
            }
            
            QScrollBar::handle:vertical {
                background-color: #454545;
                min-height: 20px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #505050;
            }
            
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                height: 0;
            }
            
            QScrollBar:horizontal {
                background-color: #242424;
                height: 14px;
                margin: 0;
            }
            
            QScrollBar::handle:horizontal {
                background-color: #454545;
                min-width: 20px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background-color: #505050;
            }
            
            QScrollBar::add-line:horizontal,
            QScrollBar::sub-line:horizontal {
                width: 0;
            }
                min-height: 24px;
            }
            
            QSpinBox::up-button, QDoubleSpinBox::up-button {
                subcontrol-origin: border;
                subcontrol-position: center right;
                width: 22px;
                border-left: 1px solid #404040;
                background-color: #505050;
                margin-right: 22px;  /* Space for down button */
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
                background-color: #606060;
            }
            
            QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed {
                background-color: #404040;
            }
            
            QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
                image: none;
                width: 7px;
                height: 7px;
                background: transparent;
                border-left: 2px solid #ffffff;
                border-top: 2px solid #ffffff;
                transform: rotate(45deg);
                margin-top: 5px;
            }
            
            QSpinBox::up-arrow:hover, QDoubleSpinBox::up-arrow:hover {
                border-color: #cccccc;
            }
            
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                subcontrol-origin: border;
                subcontrol-position: bottom right;
                width: 20px;
                border-left: 1px solid #404040;
                border-top: 1px solid #404040;
                background-color: #505050;
                border-bottom-right-radius: 3px;
            }
            
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #606060;
            }
            
            QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
                background-color: #404040;
            }
            
            QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
                image: none;
                width: 7px;
                height: 7px;
                background: transparent;
                border-left: 2px solid #ffffff;
                border-top: 2px solid #ffffff;
                transform: rotate(225deg);
                margin-bottom: 5px;
            }
            
            QSpinBox::down-arrow:hover, QDoubleSpinBox::down-arrow:hover {
                border-color: #cccccc;
            }
                margin: auto;
            }
            

            
            QTextEdit {
                background-color: #353535;
                border: 1px solid #404040;
                border-radius: 4px;
                color: #ffffff;
            }
            
            QProgressBar {
                border: 1px solid #404040;
                border-radius: 4px;
                text-align: center;
            }
            
            QProgressBar::chunk {
                background-color: #2a82da;
            }
            
            QTabWidget::pane {
                border: 1px solid #404040;
            }
            
            QTabBar::tab {
                background-color: #353535;
                border: 1px solid #404040;
                padding: 5px;
            }
            
            QTabBar::tab:selected {
                background-color: #404040;
            }
            """
        else:  # Light theme
            return common_style + """
            /* Light theme */
            QMainWindow, QDialog {
                background-color: #ffffff;
                color: #2d2d2d;
            }
            
            QWidget {
                background-color: #ffffff;
                color: #2d2d2d;
            }
            
            QGroupBox {
                border-color: #e0e0e0;
                background-color: #f8f9fa;
            }
            
            QGroupBox::title {
                color: #2d2d2d;
                background-color: #ffffff;
            }
            
            /* Elegant gradient buttons */
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #dee2e6;
                color: #2d2d2d;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #f8f9fa, stop:1 #e9ecef);
                border-color: #ced4da;
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #e9ecef, stop:1 #dee2e6);
            }
            
            /* Input fields */
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                background-color: #ffffff;
                border: 1px solid #ced4da;
                color: #2d2d2d;
            }
            
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border-color: #80bdff;
                background-color: #ffffff;
                box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
            }
            
            /* Spinbox styling */
            QSpinBox, QDoubleSpinBox {
                min-width: 90px;
                padding-right: 20px;
            }
            
            QSpinBox::up-button, QDoubleSpinBox::up-button,
            QSpinBox::down-button, QDoubleSpinBox::down-button {
                width: 20px;
                border-radius: 4px;
                border: 1px solid #ced4da;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #ffffff, stop:1 #f8f9fa);
            }
            
            QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
            QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #f8f9fa, stop:1 #e9ecef);
                border-color: #adb5bd;
            }
            
            QSpinBox::up-button:pressed, QDoubleSpinBox::up-button:pressed,
            QSpinBox::down-button:pressed, QDoubleSpinBox::down-button:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #e9ecef, stop:1 #dee2e6);
            }
            
            QSpinBox::up-arrow, QDoubleSpinBox::up-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-bottom: 6px solid #495057;
            }
            
            QSpinBox::down-arrow, QDoubleSpinBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #495057;
            }
            
            /* Tab styling */
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: #ffffff;
            }
            
            QTabBar::tab {
                background-color: #f8f9fa;
                color: #6c757d;
                border: 1px solid #dee2e6;
                padding: 8px 16px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            
            QTabBar::tab:selected {
                background-color: #ffffff;
                color: #2d2d2d;
                border-bottom-color: #ffffff;
            }
            
            QTabBar::tab:hover:!selected {
                background-color: #e9ecef;
                color: #2d2d2d;
            }
            
            /* Scrollbars */
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 14px;
                margin: 0;
            }
            
            QScrollBar::handle:vertical {
                background-color: #ced4da;
                min-height: 20px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #adb5bd;
            }
            
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                height: 0;
            }
            
            QScrollBar:horizontal {
                background-color: #f8f9fa;
                height: 14px;
                margin: 0;
            }
            
            QScrollBar::handle:horizontal {
                background-color: #ced4da;
                min-width: 20px;
                border-radius: 7px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background-color: #adb5bd;
            }
            
            QScrollBar::add-line:horizontal,
            QScrollBar::sub-line:horizontal {
                width: 0;
            }"""
            
    def toggle_theme(self):
        """Toggle between light and dark themes."""
        self.current_theme = "dark" if self.current_theme == "light" else "light"
        self._save_theme_settings()
        QApplication.instance().setStyleSheet(self.get_theme_stylesheet())
        self.theme_changed.emit(self.current_theme)
