#!/usr/bin/env python3
"""
Main window implementation for AreTomo3 GUI.
"""
import os
import sys
import re
import glob
import subprocess
import logging
import platform
import psutil
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
from datetime import datetime

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QPushButton, QLabel, QGroupBox, QLineEdit, QSpinBox, QDoubleSpinBox,
    QCheckBox, QComboBox, QListWidget, QProgressBar, QTabWidget, QScrollArea,
    QMessageBox, QFileDialog, QTextEdit, QTableWidget, QTableWidgetItem,
    QSplitter, QStatusBar, QApplication, QSlider
)
# Import our enhanced spinbox implementation
try:
    from .simple_spinbox import Enhanced<PERSON><PERSON>Box, EnhancedDoubleSpinBox
except ImportError:
    from gui.simple_spinbox import Enhanced<PERSON>pinBox, EnhancedDouble<PERSON>pinBox
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QAction, QImage, QPixmap
from PyQt6.QtCore import pyqtSlot, QT_VERSION_STR, QThread, QTimer, pyqtSignal

import mrcfile
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

from .viewers.mrc_viewer import MRCViewer
from .viewers.analysis_viewer import AnalysisViewer
# from .viewers.visualization import MetricsDashboard  # Temporarily commented out due to scipy issue
from .viewers.preview_grid import PreviewGridView
from .widgets.resource_monitor import ResourceMonitor
from .advanced_settings_tab import AdvancedSettingsTab
from .theme_manager import ThemeManager
from ..core.error_handling import FileSystemError, handle_exception, try_operation
from ..core.thread_manager import get_thread_manager, TaskPriority
from ..core.file_watcher import FileWatcher
from ..core.system_monitor import GPUMonitor, SystemMonitor
from ..core.config.config_validation import (
    validate_aretomo_installation,
    validate_input_files,
    validate_output_directory,
    AreTomo3Config
)
from ..core.config.config import ARETOMO3_SETTINGS, MICROSCOPE_SETTINGS
from ..utils.mdoc_parser import parse_mdoc
from ..utils.export_functions import (
    export_to_mrc,
    export_to_tiff,
    export_to_relion,
    export_to_eman,
    export_to_imagej
)
from .widgets.batch_processing import BatchProcessingWidget

logger = logging.getLogger(__name__)

__all__ = ['AreTomo3GUI']

class SystemResourceMonitor(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Create labels for system resources
        self.cpu_label = QLabel("CPU Usage: ---%")
        self.memory_label = QLabel("Memory Usage: ---%")
        self.disk_label = QLabel("Disk Usage: ---%")
        self.gpu_label = QLabel("GPU: Checking...")

        # Add labels to layout
        layout.addWidget(self.cpu_label)
        layout.addWidget(self.memory_label)
        layout.addWidget(self.disk_label)
        layout.addWidget(self.gpu_label)

        # Initialize system monitor
        self.monitor = SystemMonitor(update_interval=2.0)
        self.monitor.start()

        # Setup update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_stats)
        self.update_timer.start(2000)  # Update every 2 seconds

    def update_stats(self):
        """Update system statistics display"""
        data = self.monitor.get_latest_data()
        if not data:
            return

        if 'error' in data:
            self.cpu_label.setText(f"Error: {data['error']}")
            return

        self.cpu_label.setText(f"CPU Usage: {data.get('cpu', 0):.1f}%")

        mem = data.get('memory', {})
        if mem:
            used_gb = mem.get('used', 0) / (1024**3)
            total_gb = mem.get('total', 0) / (1024**3)
            self.memory_label.setText(f"Memory: {used_gb:.1f}GB / {total_gb:.1f}GB ({mem.get('percent', 0)}%)")

        disk = data.get('disk', {})
        if disk:
            used_gb = disk.get('used', 0) / (1024**3)
            total_gb = disk.get('total', 0) / (1024**3)
            self.disk_label.setText(f"Disk: {used_gb:.1f}GB / {total_gb:.1f}GB ({disk.get('percent', 0)}%)")

        gpu = data.get('gpu')
        if gpu:
            self.gpu_label.setText(
                f"GPU: {gpu.get('name', 'Unknown')} - "
                f"Mem: {gpu.get('memory_used', 0)}MB / {gpu.get('memory_total', 0)}MB - "
                f"Temp: {gpu.get('temperature', 0)}°C - "
                f"Load: {gpu.get('utilization', 0)}%"
            )
        else:
            self.gpu_label.setText("GPU: Not available")

    def closeEvent(self, event):
        """Clean up when widget is closed"""
        self.monitor.stop()
        super().closeEvent(event)

class TiltSeries:
    def __init__(self, position_name):
        self.position_name = position_name
        self.files = []          # List of .eer files
        self.angles = []         # List of tilt angles
        self.mdoc_params = None  # Parameters from .mdoc file
        self.mdoc_file = None    # Path to .mdoc file
        self.gain_ref = None     # Path to gain reference file
        self.series_params = {   # Processing parameters (from mdoc or GUI)
            'pixel_size': 1.91,  # Default from example
            'tilt_axis': -95.75, # Default from example
            'voltage': 300,      # Default from example
            'cs': 2.7,          # Default from example
            'amp_contrast': 0.1, # Default from example
            'frame_dose': 0.14   # Default from example
        }
        # AreTomo3 specific parameters
        self.aretomo_params = {
            'mc_bin': 1,         # Motion correction binning
            'mc_patch': [1, 1],  # Motion correction patch size
            'fm_int': 12,        # Frame interval
            'split_sum': 1,      # Split sum option
            'vol_z': 2048,       # Volume Z size
            'dark_tol': 0.7,     # Dark tolerance
            'flip_gain': 1,      # Flip gain flag
            'flip_vol': 1,       # Flip volume flag
            'out_imod': 1,       # Output IMOD files
            'out_xf': 1,         # Output transform files
            'wbp': 1,           # Weighted back-projection
            'tilt_cor': 1,       # Tilt correction
            'patch': [0, 0],     # Patch size
            'ctf': [1, 15],      # CTF correction [flag, lowpass]
            'at_bin': 4         # Alignment binning
        }

    def add_file(self, filepath, angle):
        """Add a file to the tilt series and try to find its .mdoc file."""
        self.files.append(filepath)
        self.angles.append(angle)

        # Try different patterns to find the corresponding .mdoc file
        possible_mdoc_files = [
            filepath.replace('_EER.eer', '.mdoc'),  # Standard pattern
            os.path.join(os.path.dirname(filepath), f"{self.position_name}.mdoc"),  # Position-based
            os.path.splitext(filepath)[0] + '.mdoc'  # Simple extension replacement
        ]

        # Try each possible mdoc file location
        for mdoc_file in possible_mdoc_files:
            if os.path.exists(mdoc_file):
                self.mdoc_file = mdoc_file
                try:
                    self.mdoc_params = parse_mdoc(mdoc_file)
                    print(f"Successfully loaded mdoc file: {mdoc_file}")

                    # Update series parameters from mdoc file
                    if self.mdoc_params:
                        for key in self.series_params.keys():
                            if key in self.mdoc_params and self.series_params[key] is None:
                                self.series_params[key] = self.mdoc_params[key]

                    # Look for gain reference file in the same directory
                    gain_files = ['GainReference.gain', 'gain.dm4', 'gain.mrc']
                    for gain_file in gain_files:
                        gain_path = os.path.join(os.path.dirname(filepath), gain_file)
                        if os.path.exists(gain_path):
                            self.gain_ref = gain_path
                            print(f"Found gain reference: {gain_path}")
                            break

                    print(f"Series parameters: {self.series_params}")
                    break
                except Exception as e:
                    print(f"Error parsing mdoc file {mdoc_file}: {str(e)}")
                    logger.error(f"Error parsing mdoc file: {str(e)}")
                break

    def parse_mdoc(self, mdoc_file):
        """Parse MDOC file to extract parameters."""
        try:
            with open(mdoc_file, 'r') as f:
                content = f.read()

            # Get basic parameters from header with careful error handling
            pixel_match = re.search(r'PixelSpacing\s*=\s*([\d.]+)', content)
            if pixel_match:
                self.series_params['pixel_size'] = float(pixel_match.group(1))

            voltage_match = re.search(r'Voltage\s*=\s*([\d.]+)', content)
            if voltage_match:
                self.series_params['voltage'] = int(float(voltage_match.group(1)))

            # Get Cs value
            cs_match = re.search(r'Cs\s*=\s*([\d.]+)', content)
            if cs_match:
                self.series_params['cs'] = float(cs_match.group(1))

            # Get amplitude contrast
            amp_match = re.search(r'AmplitudeContrast\s*=\s*([\d.]+)', content)
            if amp_match:
                self.series_params['amp_contrast'] = float(amp_match.group(1))

            # Get tilt axis from header if available
            tilt_axis_match = re.search(r'TiltAxisAngle\s*=\s*([-\d.]+)', content)
            if tilt_axis_match:
                self.series_params['tilt_axis'] = float(tilt_axis_match.group(1))

            # Get frame dose if available
            dose_match = re.search(r'ExposureDose\s*=\s*([\d.]+)', content)
            if dose_match:
                self.series_params['frame_dose'] = float(dose_match.group(1))

            # Log the parameters we found
            logger.info(f"Parameters found in MDOC for {self.position_name}: {self.series_params}")

            # Extract tilt angles
            angles = []
            for match in re.finditer(r'\[ZValue = \d+\]\s*\nTiltAngle\s*=\s*([-\d.]+)', content):
                angles.append(float(match.group(1)))

            if angles:
                self.angles = angles

            logger.info(f"Successfully parsed MDOC file: {mdoc_file}")
            self.mdoc_file = mdoc_file
            self.mdoc_params = content  # Store full content for reference

        except Exception as e:
            logger.error(f"Error parsing MDOC file {mdoc_file}: {str(e)}")
            raise

    def sort_by_angle(self):
        # Sort files by tilt angle
        sorted_pairs = sorted(zip(self.angles, self.files))
        self.angles, self.files = map(list, zip(*sorted_pairs))

    @staticmethod
    def parse_tilt_series(directory):
        # Regular expression to match tilt series files and extract position and angle
        # Original pattern for files like: Position_1_001_0.00_20250514_142623_EER.eer
        position_pattern = r"Position_(\d+(?:_\d+)?)_\d+_([-\d.]+)_\d+_\d+_EER\.eer$"

        # Alternative pattern for files like: tomo69[41.00]_EER.eer
        tomo_pattern = r"(tomo\d+)\[([-\d.]+)\]_EER\.eer$"

        series_dict = {}

        # Check if directory exists
        if not os.path.exists(directory):
            logger.error(f"Directory does not exist: {directory}")
            return {}

        # List all relevant files
        eer_files = glob.glob(os.path.join(directory, "*.eer"))
        mdoc_files = glob.glob(os.path.join(directory, "*.mdoc"))
        gain_files = glob.glob(os.path.join(directory, "*gain*"), recursive=True)

        if not eer_files:
            logger.warning(f"No .eer files found in directory: {directory}")
            return {}

        # Process each file
        for file in eer_files:
            try:
                basename = os.path.basename(file)
                # Try position pattern first
                match = re.search(position_pattern, basename)
                if match:
                    position = match.group(1)
                    angle = float(match.group(2))

                    series_name = f"Position_{position}"
                    if series_name not in series_dict:
                        series_dict[series_name] = TiltSeries(series_name)

                    series_dict[series_name].add_file(file, angle)
                    continue

                # Try tomo pattern if position pattern didn't match
                match = re.search(tomo_pattern, basename)
                if match:
                    position = match.group(1)  # tomo69
                    angle = float(match.group(2))

                    if position not in series_dict:
                        series_dict[position] = TiltSeries(position)

                    series_dict[position].add_file(file, angle)
                    continue

                logger.warning(f"File doesn't match any known pattern: {basename}")

            except Exception as e:
                logger.error(f"Error processing file {file}: {str(e)}")
                continue

        # Try to find and load corresponding .mdoc files
        for series in series_dict.values():
            # Try to find mdoc file for this series
            mdoc_matches = [f for f in mdoc_files if series.position_name in f]
            if mdoc_matches:
                try:
                    series.parse_mdoc(mdoc_matches[0])
                    logger.info(f"Loaded MDOC file for {series.position_name}")
                except Exception as e:
                    logger.error(f"Error loading MDOC for {series.position_name}: {str(e)}")

        # Try to find gain reference file
        if gain_files:
            gain_ref = None
            for f in gain_files:
                if 'GainReference' in f:
                    gain_ref = f
                    break
            if gain_ref:
                logger.info(f"Found gain reference file: {gain_ref}")
                # Add gain ref to all series
                for series in series_dict.values():
                    series.gain_ref = gain_ref

        # Sort each series by angle
        for series in series_dict.values():
            if series.files:  # Only sort if there are files
                series.sort_by_angle()

        return series_dict

    def validate(self):
        """Validate that the tilt series has required data."""
        if not self.files:
            raise ValueError("Tilt series has no files")
        if not self.angles:
            raise ValueError("Tilt series has no angles")
        if len(self.files) != len(self.angles):
            raise ValueError("Number of files does not match number of angles")
        return True

class AreTomoWorker(QThread):
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, command):
        super().__init__()
        self.command = command

    def run(self):
        try:
            # Get path to wrapper script
            wrapper_script = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils', 'aretomo3_wrapper.sh')

            # Construct wrapped command
            wrapped_command = f"{wrapper_script} {self.command}"
            logger.info(f"Starting AreTomo3 process with command: {wrapped_command}")

            process = subprocess.Popen(
                wrapped_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True,
                shell=True
            )

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output = output.strip()
                    logger.info(f"AreTomo3: {output}")
                    self.progress.emit(output)

            rc = process.poll()
            _, stderr = process.communicate()

            if rc == 0:
                logger.info("AreTomo3 process completed successfully")
                self.finished.emit(True, "Processing completed successfully!")
            else:
                logger.error(f"AreTomo3 process failed: {stderr}")
                self.finished.emit(False, f"Error during processing: {stderr}")
        except Exception as e:
            logger.error(f"Error running AreTomo3 process: {str(e)}", exc_info=True)
            self.finished.emit(False, f"Error: {str(e)}")

class AreTomoGUI(QMainWindow):
    def __init__(self):
        """Initialize the AreTomo3 GUI main window."""
        try:
            logger.info("Creating AreTomo3 GUI window")
            super().__init__()

            # Initialize theme manager
            self.theme_manager = ThemeManager()
            QApplication.instance().setStyleSheet(self.theme_manager.get_theme_stylesheet())

            # Set window title and flags
            self.setWindowTitle("AreTomo3 GUI")
            self.setWindowFlags(self.windowFlags() | Qt.WindowType.Window | Qt.WindowType.WindowMinimizeButtonHint | Qt.WindowType.WindowMaximizeButtonHint)
            self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)

            # Set window size to 90% of screen size
            screen = QApplication.primaryScreen().availableGeometry()
            width = int(screen.width() * 0.9)  # Use 90% of screen width
            height = int(screen.height() * 0.9)  # Use 90% of screen height
            self.resize(width, height)
            self.setMinimumSize(800, 600)  # Set minimum size to ensure usability
            # Center the window on screen
            self.move(screen.center() - self.frameGeometry().center())

            # Initialize logging widget
            logger.info("Setting up logging widget")
            self.log_text = QTextEdit()
            self.log_text.setReadOnly(True)
            font = self.log_text.font()
            font.setPointSize(10)
            self.log_text.setFont(font)
            self.log_text.setLineWrapMode(QTextEdit.LineWrapMode.WidgetWidth)

            # Set up status bar
            self.status_bar = QStatusBar(self)
            self.setStatusBar(self.status_bar)
            self.status_bar.showMessage("Initializing...")

            # Initialize UI components
            logger.info("Initializing UI components")
            self.init_ui()

            # Initialize processing state
            logger.info("Initializing processing state")
            self.current_worker: Optional[AreTomoWorker] = None
            self.tilt_series: Dict[str, TiltSeries] = {}
            self.file_watcher: Optional[FileWatcher] = None
            self.processing_queue: List[Dict[str, Any]] = []
            self.is_processing: bool = False

            # Create initial AreTomo3Config with defaults
            logger.info("Creating default configuration")
            self.config = AreTomo3Config(pixel_size=1.91)  # Only required parameter

            # Update status
            self.status_bar.showMessage("Ready")
            logger.info("AreTomo3 GUI window initialized successfully")

        except Exception as e:
            logger.critical(f"Failed to initialize AreTomo3 GUI window: {e}", exc_info=True)
            raise

    def log_message(self, message: str, level: str = "INFO") -> None:
        """Log messages to both the GUI log widget and the application logger."""
        # Add to GUI log widget
        if hasattr(self, 'log_text'):
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {level}: {message}"
            self.log_text.append(formatted_message)

        # Also log to application logger
        if level.upper() == "DEBUG":
            logger.debug(message)
        elif level.upper() == "INFO":
            logger.info(message)
        elif level.upper() == "WARNING":
            logger.warning(message)
        elif level.upper() == "ERROR":
            logger.error(message)
        elif level.upper() == "CRITICAL":
            logger.critical(message)
        else:
            logger.info(f"{level}: {message}")

    def init_ui(self) -> None:
        """Initialize the user interface."""
        try:
            logger.info("Setting up main menu")
            # Set window title
            self.setWindowTitle("AreTomo3 GUI")

            # Set up main menu
            menubar = self.menuBar()
            file_menu = menubar.addMenu("File")
            help_menu = menubar.addMenu("Help")
            exit_action = QAction("Exit", self)
            exit_action.triggered.connect(self.close)
            file_menu.addAction(exit_action)
            about_action = QAction("About", self)
            about_action.triggered.connect(self._show_about)
            help_menu.addAction(about_action)

            logger.info("Setting up central widget")
            # Set up central widget and main layout
            self.central_widget = QWidget(self)
            self.setCentralWidget(self.central_widget)
            main_layout = QVBoxLayout(self.central_widget)

            # Create tab widget
            logger.info("Creating tab widget")
            self.tabs = QTabWidget(self.central_widget)
            self.tabs.setDocumentMode(True)
            self.tabs.setMovable(True)

            # Create tab pages with parents
            logger.info("Creating tab pages")
            self.main_tab = QWidget(self.tabs)
            self.analysis_tab = QWidget(self.tabs)
            self.viewer_tab = QWidget(self.tabs)
            self.batch_tab = QWidget(self.tabs)
            self.export_tab = QWidget(self.tabs)
            self.queue_monitor_tab = QWidget(self.tabs)  # Combined queue and monitor
            self.log_tab = QWidget(self.tabs)

            # Create advanced settings tab
            logger.info("Creating advanced settings tab")
            self.advanced_tab = AdvancedSettingsTab(self.tabs)

            # Set up each tab's content
            logger.info("Setting up main tab")
            main_layout_obj = QVBoxLayout(self.main_tab)
            self.setup_main_tab(self.main_tab, main_layout_obj)

            logger.info("Setting up combined queue and monitor tab")
            queue_monitor_layout = QVBoxLayout(self.queue_monitor_tab)
            self.setup_queue_monitor_tab(self.queue_monitor_tab, queue_monitor_layout)

            logger.info("Setting up analysis tab")
            analysis_layout = QVBoxLayout(self.analysis_tab)
            self.setup_analysis_tab(self.analysis_tab, analysis_layout)

            logger.info("Setting up viewer tab")
            viewer_layout = QVBoxLayout(self.viewer_tab)
            self.setup_viewer_tab(self.viewer_tab, viewer_layout)

            logger.info("Setting up batch tab")
            batch_layout = QVBoxLayout(self.batch_tab)
            self.setup_batch_tab(self.batch_tab, batch_layout)

            logger.info("Setting up export tab")
            export_layout = QVBoxLayout(self.export_tab)
            self.setup_export_tab(self.export_tab, export_layout)

            logger.info("Setting up log tab")
            log_layout = QVBoxLayout(self.log_tab)
            self.setup_log_tab(self.log_tab, log_layout)

            # Add tabs to widget - reorganized order with queue/monitor at end
            self.tabs.addTab(self.main_tab, "Main")
            self.tabs.addTab(self.advanced_tab, "Advanced")
            self.tabs.addTab(self.analysis_tab, "Analysis")
            self.tabs.addTab(self.viewer_tab, "Viewer")
            self.tabs.addTab(self.batch_tab, "Batch")
            self.tabs.addTab(self.export_tab, "Export")
            self.tabs.addTab(self.queue_monitor_tab, "Queue & Monitor")
            self.tabs.addTab(self.log_tab, "Log")

            # Add tabs widget to main layout
            main_layout.addWidget(self.tabs)

            logger.info("UI initialization completed successfully")
        except Exception as e:
            logger.critical(f"Failed to initialize UI: {e}", exc_info=True)
            raise

    def setup_main_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """
        Set up the main tab with input/output settings and quick actions.

        Args:
            tab_widget: The parent widget for the main tab
            layout: The layout to add widgets to
        """
        try:
            # Create a scroll area and main container widget
            scroll = QScrollArea(tab_widget)
            scroll.setWidgetResizable(True)
            scroll_container = QWidget()
            main_layout = QVBoxLayout(scroll_container)
            main_layout.setSpacing(10)  # Add spacing between sections

            # Welcome message
            welcome_label = QLabel("Welcome to AreTomo3 GUI", tab_widget)
            welcome_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
            main_layout.addWidget(welcome_label)

            # AreTomo3 basic settings
            aretomo_group = QGroupBox("AreTomo3 Settings", tab_widget)
            aretomo_layout = QGridLayout()
            aretomo_layout.setColumnStretch(1, 1)  # Make path field expand

            # AreTomo3 path
            self.aretomo_path = QLineEdit(aretomo_group)
            self.aretomo_path.setText(os.environ.get('ARETOMO3_PATH', ''))
            browse_aretomo_btn = QPushButton("Browse...", aretomo_group)
            browse_aretomo_btn.clicked.connect(self.on_browse_aretomo)
            aretomo_layout.addWidget(QLabel("AreTomo3 Path:", aretomo_group), 0, 0)
            aretomo_layout.addWidget(self.aretomo_path, 0, 1)
            aretomo_layout.addWidget(browse_aretomo_btn, 0, 2)

            # GPU index selector
            self.gpu_index = QSpinBox(aretomo_group)
            self.gpu_index.setRange(0, 7)
            self.gpu_index.setToolTip("Select which GPU to use (0-7)")
            aretomo_layout.addWidget(QLabel("GPU Index:", aretomo_group), 1, 0)
            aretomo_layout.addWidget(self.gpu_index, 1, 1)
            aretomo_group.setLayout(aretomo_layout)
            main_layout.addWidget(aretomo_group)

            # Input/Output section
            io_group = QGroupBox("Input/Output Settings", tab_widget)
            io_layout = QGridLayout()
            io_layout.setColumnStretch(1, 1)  # Make path fields expand

            # Input directory
            self.input_dir = QLineEdit(io_group)
            browse_input_btn = QPushButton("Browse...", io_group)
            browse_input_btn.clicked.connect(self.on_browse_input)
            io_layout.addWidget(QLabel("Input Directory:", io_group), 0, 0)
            io_layout.addWidget(self.input_dir, 0, 1)
            io_layout.addWidget(browse_input_btn, 0, 2)

            # Output directory
            self.output_dir = QLineEdit(io_group)
            browse_output_btn = QPushButton("Browse...", io_group)
            browse_output_btn.clicked.connect(self.on_browse_output)
            io_layout.addWidget(QLabel("Output Directory:", io_group), 1, 0)
            io_layout.addWidget(self.output_dir, 1, 1)
            io_layout.addWidget(browse_output_btn, 1, 2)

            io_group.setLayout(io_layout)
            main_layout.addWidget(io_group)

            # Quick Actions section
            actions_group = QGroupBox("Quick Actions", tab_widget)
            actions_layout = QHBoxLayout()
            actions_layout.setSpacing(10)

            load_btn = QPushButton("Load Tilt Series", actions_group)
            load_btn.clicked.connect(self.on_load_tilt_series)
            process_btn = QPushButton("Process", actions_group)
            process_btn.clicked.connect(self.on_process)
            stop_btn = QPushButton("Stop", actions_group)
            stop_btn.clicked.connect(self.on_stop)

            actions_layout.addWidget(load_btn)
            actions_layout.addWidget(process_btn)
            actions_layout.addWidget(stop_btn)
            actions_group.setLayout(actions_layout)
            main_layout.addWidget(actions_group)

            # Add command preview section before the parameters
            command_group = QGroupBox("Command Preview", tab_widget)
            command_layout = QVBoxLayout()
            command_layout.setSpacing(5)
            command_layout.setContentsMargins(10, 10, 10, 10)

            # Add preview button and command text in horizontal layout
            preview_container = QWidget()
            preview_hlayout = QHBoxLayout(preview_container)
            preview_hlayout.setContentsMargins(0, 0, 0, 0)
            preview_hlayout.setSpacing(10)

            self.command_preview = QTextEdit()
            self.command_preview.setReadOnly(True)
            self.command_preview.setMaximumHeight(60)
            self.command_preview.setStyleSheet("font-family: monospace;")
            preview_hlayout.addWidget(self.command_preview)

            preview_btn = QPushButton("Preview Command")
            preview_btn.setFixedWidth(150)  # Make button wider
            preview_btn.setMinimumHeight(40)  # Make button taller
            preview_btn.setStyleSheet("""
                QPushButton {
                    font-size: 12px;
                    font-weight: bold;
                    padding: 8px;
                }
            """)
            preview_btn.clicked.connect(self._preview_command)
            preview_hlayout.addWidget(preview_btn)

            command_layout.addWidget(preview_container)
            command_group.setLayout(command_layout)
            main_layout.addWidget(command_group)

            # Essential Parameters - simplified for main tab
            essential_group = QGroupBox("Essential Parameters")
            essential_layout = QFormLayout()
            essential_layout.setSpacing(12)

            self.pixel_size = QDoubleSpinBox()
            self.pixel_size.setRange(0.1, 100.0)
            self.pixel_size.setValue(1.91)
            self.pixel_size.setSuffix(" Å")
            self.pixel_size.setToolTip("Pixel size of the detector")
            essential_layout.addRow("Pixel Size:", self.pixel_size)

            self.voltage = QSpinBox()
            self.voltage.setRange(60, 300)
            self.voltage.setValue(300)
            self.voltage.setSuffix(" kV")
            self.voltage.setToolTip("Acceleration voltage of the microscope")
            essential_layout.addRow("Voltage:", self.voltage)

            self.tilt_axis = QDoubleSpinBox()
            self.tilt_axis.setRange(-180, 180)
            self.tilt_axis.setValue(-95.75)
            self.tilt_axis.setSuffix("°")
            self.tilt_axis.setToolTip("Tilt axis angle (use Advanced tab for auto-search)")
            essential_layout.addRow("Tilt Axis:", self.tilt_axis)

            self.volume_z = QSpinBox()
            self.volume_z.setRange(64, 8192)
            self.volume_z.setValue(2048)
            self.volume_z.setToolTip("Z dimension of output tomogram")
            essential_layout.addRow("Volume Z Size:", self.volume_z)

            essential_group.setLayout(essential_layout)
            main_layout.addWidget(essential_group)

            # Quick Processing Options - simplified for main tab
            quick_options_group = QGroupBox("Quick Processing Options")
            quick_options_layout = QGridLayout()
            quick_options_layout.setSpacing(15)

            # Essential checkboxes only
            self.correct_ctf = QCheckBox("Correct CTF")
            self.correct_ctf.setChecked(True)
            self.correct_ctf.setToolTip("Apply CTF correction during reconstruction")

            self.out_imod = QCheckBox("Output IMOD Files")
            self.out_imod.setChecked(True)
            self.out_imod.setToolTip("Generate IMOD-compatible output files")

            self.wbp = QCheckBox("Use WBP")
            self.wbp.setChecked(True)
            self.wbp.setToolTip("Use Weighted Back Projection algorithm")

            quick_options_layout.addWidget(self.correct_ctf, 0, 0)
            quick_options_layout.addWidget(self.out_imod, 0, 1)
            quick_options_layout.addWidget(self.wbp, 1, 0)

            # Add note about advanced settings
            advanced_note = QLabel("💡 For detailed motion correction, binning, and other advanced parameters, use the Advanced tab")
            advanced_note.setStyleSheet("color: #666666; font-style: italic; padding: 10px;")
            advanced_note.setWordWrap(True)
            quick_options_layout.addWidget(advanced_note, 2, 0, 1, 2)

            quick_options_group.setLayout(quick_options_layout)
            main_layout.addWidget(quick_options_group)

            # Add a stretch at the end to keep everything at the top
            main_layout.addStretch()

            # Set the scroll area widget
            scroll.setWidget(scroll_container)

            # Add scroll area to tab layout
            layout.addWidget(scroll)

        except Exception as e:
            logger.error(f"Error setting up main tab: {str(e)}", exc_info=True)
            raise

    def setup_queue_monitor_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the combined queue and monitor tab for managing jobs and system monitoring."""
        # Create horizontal splitter to divide queue and monitor sections
        splitter = QSplitter(Qt.Orientation.Horizontal, tab_widget)

        # Left side - Queue Management
        queue_widget = QWidget()
        queue_layout = QVBoxLayout(queue_widget)

        queue_group = QGroupBox("Processing Queue")
        queue_group_layout = QVBoxLayout(queue_group)
        self.queue_list = QListWidget()
        queue_group_layout.addWidget(self.queue_list)

        # Queue controls
        queue_controls_layout = QHBoxLayout()
        remove_btn = QPushButton("Remove Selected")
        remove_btn.clicked.connect(self.on_remove_from_queue)
        clear_btn = QPushButton("Clear Queue")
        clear_btn.clicked.connect(self.on_clear_queue)
        queue_controls_layout.addWidget(remove_btn)
        queue_controls_layout.addWidget(clear_btn)
        queue_controls_layout.addStretch()

        queue_group_layout.addLayout(queue_controls_layout)
        queue_layout.addWidget(queue_group)

        # Processing Status section for queue side
        status_group = QGroupBox("Processing Status")
        status_layout = QVBoxLayout(status_group)
        self.progress_bar = QProgressBar()
        status_layout.addWidget(self.progress_bar)
        self.status_label = QLabel("Idle")
        status_layout.addWidget(self.status_label)
        queue_layout.addWidget(status_group)

        queue_layout.addStretch()

        # Right side - System Monitor
        monitor_widget = QWidget()
        monitor_layout = QVBoxLayout(monitor_widget)

        monitor_group = QGroupBox("System Resources")
        monitor_group_layout = QVBoxLayout(monitor_group)
        self.sys_monitor = SystemResourceMonitor()
        monitor_group_layout.addWidget(self.sys_monitor)
        monitor_layout.addWidget(monitor_group)

        monitor_layout.addStretch()

        # Add widgets to splitter
        splitter.addWidget(queue_widget)
        splitter.addWidget(monitor_widget)
        splitter.setStretchFactor(0, 1)  # Queue side gets equal space
        splitter.setStretchFactor(1, 1)  # Monitor side gets equal space

        layout.addWidget(splitter)

    def setup_analysis_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the enhanced analysis tab for comprehensive data visualization and analysis."""
        # Create main horizontal splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel - Analysis Tools and Controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(10)

        # Analysis Type Selection
        analysis_group = QGroupBox("Analysis Type")
        analysis_layout = QVBoxLayout(analysis_group)

        self.analysis_type = QComboBox()
        self.analysis_type.addItems([
            "Motion Correction Analysis",
            "CTF Estimation Results",
            "Tilt Series Quality",
            "Reconstruction Metrics",
            "Dose Distribution",
            "Resolution Assessment",
            "Particle Distribution",
            "Statistical Analysis"
        ])
        self.analysis_type.currentTextChanged.connect(self._on_analysis_type_changed)
        analysis_layout.addWidget(self.analysis_type)
        left_layout.addWidget(analysis_group)

        # File Selection
        file_group = QGroupBox("Data Source")
        file_layout = QVBoxLayout(file_group)

        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("Select data file or directory...")
        browse_btn = QPushButton("Browse Files")
        browse_btn.clicked.connect(self._browse_analysis_files)

        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(browse_btn)
        left_layout.addWidget(file_group)

        # Analysis Parameters
        params_group = QGroupBox("Analysis Parameters")
        params_layout = QFormLayout(params_group)

        self.frame_range_start = QSpinBox()
        self.frame_range_start.setRange(0, 9999)
        self.frame_range_end = QSpinBox()
        self.frame_range_end.setRange(0, 9999)
        self.frame_range_end.setValue(100)

        frame_widget = QWidget()
        frame_layout = QHBoxLayout(frame_widget)
        frame_layout.addWidget(self.frame_range_start)
        frame_layout.addWidget(QLabel("to"))
        frame_layout.addWidget(self.frame_range_end)
        frame_layout.addStretch()
        params_layout.addRow("Frame Range:", frame_widget)

        self.bin_factor = QComboBox()
        self.bin_factor.addItems(["1", "2", "4", "8"])
        self.bin_factor.setCurrentText("2")
        params_layout.addRow("Binning Factor:", self.bin_factor)

        self.smoothing_factor = QDoubleSpinBox()
        self.smoothing_factor.setRange(0.0, 10.0)
        self.smoothing_factor.setValue(1.0)
        self.smoothing_factor.setSingleStep(0.1)
        params_layout.addRow("Smoothing:", self.smoothing_factor)

        left_layout.addWidget(params_group)

        # Analysis Actions
        actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(actions_group)

        analyze_btn = QPushButton("🔍 Run Analysis")
        analyze_btn.setStyleSheet("QPushButton { font-weight: bold; padding: 8px; }")
        analyze_btn.clicked.connect(self._run_analysis)

        export_btn = QPushButton("📊 Export Results")
        export_btn.clicked.connect(self._export_analysis)

        save_plot_btn = QPushButton("💾 Save Plot")
        save_plot_btn.clicked.connect(self._save_plot)

        actions_layout.addWidget(analyze_btn)
        actions_layout.addWidget(export_btn)
        actions_layout.addWidget(save_plot_btn)
        left_layout.addWidget(actions_group)

        # Auto-update controls
        auto_group = QGroupBox("Real-time Updates")
        auto_layout = QVBoxLayout(auto_group)

        self.auto_update = QCheckBox("Auto-refresh every")
        self.auto_update.setChecked(True)
        self.update_interval = QSpinBox()
        self.update_interval.setRange(1, 60)
        self.update_interval.setValue(5)
        self.update_interval.setSuffix(" sec")

        auto_widget = QWidget()
        auto_widget_layout = QHBoxLayout(auto_widget)
        auto_widget_layout.addWidget(self.auto_update)
        auto_widget_layout.addWidget(self.update_interval)
        auto_widget_layout.addStretch()

        auto_layout.addWidget(auto_widget)
        left_layout.addWidget(auto_group)

        left_layout.addStretch()

        # Right panel - Visualization Area
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Visualization toolbar
        viz_toolbar = QWidget()
        viz_toolbar_layout = QHBoxLayout(viz_toolbar)
        viz_toolbar_layout.setContentsMargins(5, 5, 5, 5)

        self.colormap_combo = QComboBox()
        self.colormap_combo.addItems(["viridis", "plasma", "inferno", "magma", "gray", "hot", "cool", "jet"])
        self.colormap_combo.currentTextChanged.connect(self._update_colormap)

        self.plot_type_combo = QComboBox()
        self.plot_type_combo.addItems(["Line Plot", "Scatter Plot", "Heatmap", "3D Surface", "Histogram"])
        self.plot_type_combo.currentTextChanged.connect(self._update_plot_type)

        viz_toolbar_layout.addWidget(QLabel("Colormap:"))
        viz_toolbar_layout.addWidget(self.colormap_combo)
        viz_toolbar_layout.addWidget(QLabel("Plot Type:"))
        viz_toolbar_layout.addWidget(self.plot_type_combo)
        viz_toolbar_layout.addStretch()

        right_layout.addWidget(viz_toolbar)

        # Main visualization area with tabs
        self.viz_tabs = QTabWidget()
        self.viz_tabs.setTabPosition(QTabWidget.TabPosition.South)

        # Analysis viewer
        self.analysis_viewer = AnalysisViewer()
        self.viz_tabs.addTab(self.analysis_viewer, "📈 Analysis")

        # Statistics panel
        self.stats_widget = self._create_stats_widget()
        self.viz_tabs.addTab(self.stats_widget, "📊 Statistics")

        # Comparison panel
        self.comparison_widget = self._create_comparison_widget()
        self.viz_tabs.addTab(self.comparison_widget, "⚖️ Compare")

        right_layout.addWidget(self.viz_tabs)

        # Add panels to main splitter
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(right_panel)
        main_splitter.setStretchFactor(0, 1)  # Left panel
        main_splitter.setStretchFactor(1, 3)  # Right panel (larger)

        layout.addWidget(main_splitter)

    def setup_viewer_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the enhanced viewer tab for comprehensive file viewing and analysis."""
        # Create main horizontal splitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # Left panel - File browser and controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(8)

        # File Browser Section
        browser_group = QGroupBox("File Browser")
        browser_layout = QVBoxLayout(browser_group)

        # Directory selection
        dir_layout = QHBoxLayout()
        self.current_dir_edit = QLineEdit()
        self.current_dir_edit.setPlaceholderText("Select directory to browse...")
        browse_dir_btn = QPushButton("📁 Browse")
        browse_dir_btn.clicked.connect(self._browse_viewer_directory)

        dir_layout.addWidget(self.current_dir_edit)
        dir_layout.addWidget(browse_dir_btn)
        browser_layout.addLayout(dir_layout)

        # File list with filtering
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Filter:"))
        self.file_filter = QComboBox()
        self.file_filter.addItems([
            "All Files (*.*)",
            "MRC Files (*.mrc)",
            "TIFF Files (*.tif, *.tiff)",
            "Image Files (*.png, *.jpg, *.jpeg)",
            "Data Files (*.txt, *.csv, *.dat)"
        ])
        self.file_filter.currentTextChanged.connect(self._update_file_list)
        filter_layout.addWidget(self.file_filter)
        filter_layout.addStretch()
        browser_layout.addLayout(filter_layout)

        # File list widget
        self.file_list = QListWidget()
        self.file_list.itemClicked.connect(self._on_file_selected)
        self.file_list.itemDoubleClicked.connect(self._on_file_double_clicked)
        browser_layout.addWidget(self.file_list)

        left_layout.addWidget(browser_group)

        # Viewer Controls Section
        controls_group = QGroupBox("Viewer Controls")
        controls_layout = QFormLayout(controls_group)

        # View mode selection
        self.view_mode = QComboBox()
        self.view_mode.addItems([
            "2D Slice View",
            "3D Volume Render",
            "Multi-slice Grid",
            "Projection View",
            "Orthogonal Views"
        ])
        self.view_mode.currentTextChanged.connect(self._change_view_mode)
        controls_layout.addRow("View Mode:", self.view_mode)

        # Slice navigation
        self.slice_slider = QSlider(Qt.Orientation.Horizontal)
        self.slice_slider.valueChanged.connect(self._update_slice)
        self.slice_spinbox = QSpinBox()
        self.slice_spinbox.valueChanged.connect(self._update_slice_from_spinbox)

        slice_widget = QWidget()
        slice_layout = QHBoxLayout(slice_widget)
        slice_layout.addWidget(self.slice_slider)
        slice_layout.addWidget(self.slice_spinbox)
        controls_layout.addRow("Slice:", slice_widget)

        # Contrast controls
        self.contrast_min = QSlider(Qt.Orientation.Horizontal)
        self.contrast_min.setRange(0, 100)
        self.contrast_min.setValue(0)
        self.contrast_min.valueChanged.connect(self._update_contrast)

        self.contrast_max = QSlider(Qt.Orientation.Horizontal)
        self.contrast_max.setRange(0, 100)
        self.contrast_max.setValue(100)
        self.contrast_max.valueChanged.connect(self._update_contrast)

        contrast_widget = QWidget()
        contrast_layout = QVBoxLayout(contrast_widget)
        contrast_layout.addWidget(QLabel("Min:"))
        contrast_layout.addWidget(self.contrast_min)
        contrast_layout.addWidget(QLabel("Max:"))
        contrast_layout.addWidget(self.contrast_max)
        controls_layout.addRow("Contrast:", contrast_widget)

        # Colormap selection
        self.viewer_colormap = QComboBox()
        self.viewer_colormap.addItems([
            "gray", "viridis", "plasma", "inferno", "magma",
            "hot", "cool", "jet", "rainbow", "bone"
        ])
        self.viewer_colormap.currentTextChanged.connect(self._update_viewer_colormap)
        controls_layout.addRow("Colormap:", self.viewer_colormap)

        left_layout.addWidget(controls_group)

        # Measurement Tools Section
        tools_group = QGroupBox("Measurement Tools")
        tools_layout = QVBoxLayout(tools_group)

        # Measurement buttons
        measure_distance_btn = QPushButton("📏 Distance")
        measure_distance_btn.setCheckable(True)
        measure_distance_btn.clicked.connect(self._toggle_distance_tool)

        measure_angle_btn = QPushButton("📐 Angle")
        measure_angle_btn.setCheckable(True)
        measure_angle_btn.clicked.connect(self._toggle_angle_tool)

        measure_area_btn = QPushButton("⬜ Area")
        measure_area_btn.setCheckable(True)
        measure_area_btn.clicked.connect(self._toggle_area_tool)

        profile_btn = QPushButton("📊 Line Profile")
        profile_btn.clicked.connect(self._show_line_profile)

        tools_layout.addWidget(measure_distance_btn)
        tools_layout.addWidget(measure_angle_btn)
        tools_layout.addWidget(measure_area_btn)
        tools_layout.addWidget(profile_btn)

        # Measurement results
        self.measurement_results = QTextEdit()
        self.measurement_results.setMaximumHeight(100)
        self.measurement_results.setReadOnly(True)
        tools_layout.addWidget(self.measurement_results)

        left_layout.addWidget(tools_group)

        # Export Options Section
        export_group = QGroupBox("Export Options")
        export_layout = QVBoxLayout(export_group)

        save_image_btn = QPushButton("💾 Save Image")
        save_image_btn.clicked.connect(self._save_current_view)

        save_movie_btn = QPushButton("🎬 Save Movie")
        save_movie_btn.clicked.connect(self._save_movie)

        export_data_btn = QPushButton("📋 Export Data")
        export_data_btn.clicked.connect(self._export_viewer_data)

        export_layout.addWidget(save_image_btn)
        export_layout.addWidget(save_movie_btn)
        export_layout.addWidget(export_data_btn)

        left_layout.addWidget(export_group)
        left_layout.addStretch()

        # Right panel - Enhanced viewer area
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # Viewer toolbar
        viewer_toolbar = QWidget()
        viewer_toolbar_layout = QHBoxLayout(viewer_toolbar)
        viewer_toolbar_layout.setContentsMargins(5, 5, 5, 5)

        # Zoom controls
        zoom_out_btn = QPushButton("🔍-")
        zoom_out_btn.clicked.connect(self._zoom_out)
        zoom_fit_btn = QPushButton("⬜ Fit")
        zoom_fit_btn.clicked.connect(self._zoom_fit)
        zoom_in_btn = QPushButton("🔍+")
        zoom_in_btn.clicked.connect(self._zoom_in)

        # View orientation
        self.view_orientation = QComboBox()
        self.view_orientation.addItems(["XY (Top)", "XZ (Front)", "YZ (Side)"])
        self.view_orientation.currentTextChanged.connect(self._change_orientation)

        # Scale bar toggle
        self.show_scale_bar = QCheckBox("Scale Bar")
        self.show_scale_bar.setChecked(True)
        self.show_scale_bar.toggled.connect(self._toggle_scale_bar)

        # Grid toggle
        self.show_grid = QCheckBox("Grid")
        self.show_grid.toggled.connect(self._toggle_grid)

        viewer_toolbar_layout.addWidget(zoom_out_btn)
        viewer_toolbar_layout.addWidget(zoom_fit_btn)
        viewer_toolbar_layout.addWidget(zoom_in_btn)

        # Add separator using a vertical line widget
        separator1 = QLabel("|")
        separator1.setStyleSheet("color: #666666; margin: 0 5px;")
        viewer_toolbar_layout.addWidget(separator1)

        viewer_toolbar_layout.addWidget(QLabel("View:"))
        viewer_toolbar_layout.addWidget(self.view_orientation)

        # Add another separator
        separator2 = QLabel("|")
        separator2.setStyleSheet("color: #666666; margin: 0 5px;")
        viewer_toolbar_layout.addWidget(separator2)

        viewer_toolbar_layout.addWidget(self.show_scale_bar)
        viewer_toolbar_layout.addWidget(self.show_grid)
        viewer_toolbar_layout.addStretch()

        right_layout.addWidget(viewer_toolbar)

        # Main viewer with tabs
        self.viewer_tabs = QTabWidget()
        self.viewer_tabs.setTabPosition(QTabWidget.TabPosition.South)

        # Enhanced MRC viewer
        self.mrc_viewer = MRCViewer()
        self.viewer_tabs.addTab(self.mrc_viewer, "🔬 Main View")

        # Histogram view
        self.histogram_widget = self._create_histogram_widget()
        self.viewer_tabs.addTab(self.histogram_widget, "📊 Histogram")

        # Metadata view
        self.metadata_widget = self._create_metadata_widget()
        self.viewer_tabs.addTab(self.metadata_widget, "ℹ️ Metadata")

        right_layout.addWidget(self.viewer_tabs)

        # Status bar
        self.viewer_status = QLabel("Ready")
        self.viewer_status.setStyleSheet("padding: 5px; border-top: 1px solid #ccc;")
        right_layout.addWidget(self.viewer_status)

        # Add panels to main splitter
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(right_panel)
        main_splitter.setStretchFactor(0, 1)  # Left panel
        main_splitter.setStretchFactor(1, 4)  # Right panel (much larger)

        layout.addWidget(main_splitter)

    def _create_stats_widget(self) -> QWidget:
        """Create the statistics widget for analysis tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Statistics display
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(200)
        self.stats_text.setPlaceholderText("Statistical analysis results will appear here...")
        layout.addWidget(self.stats_text)

        return widget

    def _create_comparison_widget(self) -> QWidget:
        """Create the comparison widget for analysis tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Comparison controls
        compare_layout = QHBoxLayout()
        self.compare_file1 = QLineEdit()
        self.compare_file1.setPlaceholderText("Select first file...")
        browse1_btn = QPushButton("Browse")

        self.compare_file2 = QLineEdit()
        self.compare_file2.setPlaceholderText("Select second file...")
        browse2_btn = QPushButton("Browse")

        compare_layout.addWidget(QLabel("File 1:"))
        compare_layout.addWidget(self.compare_file1)
        compare_layout.addWidget(browse1_btn)
        compare_layout.addWidget(QLabel("File 2:"))
        compare_layout.addWidget(self.compare_file2)
        compare_layout.addWidget(browse2_btn)

        layout.addLayout(compare_layout)

        # Comparison results
        self.comparison_results = QTextEdit()
        self.comparison_results.setReadOnly(True)
        self.comparison_results.setPlaceholderText("Comparison results will appear here...")
        layout.addWidget(self.comparison_results)

        return widget

    def _create_histogram_widget(self) -> QWidget:
        """Create the histogram widget for viewer tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Histogram plot
        from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
        from matplotlib.figure import Figure

        self.hist_figure = Figure(figsize=(8, 4))
        self.hist_canvas = FigureCanvas(self.hist_figure)
        self.hist_ax = self.hist_figure.add_subplot(111)
        layout.addWidget(self.hist_canvas)

        # Histogram controls
        hist_controls = QHBoxLayout()
        self.hist_bins = QSpinBox()
        self.hist_bins.setRange(10, 1000)
        self.hist_bins.setValue(100)
        self.hist_bins.valueChanged.connect(self._update_histogram)

        self.hist_log_scale = QCheckBox("Log Scale")
        self.hist_log_scale.toggled.connect(self._update_histogram)

        hist_controls.addWidget(QLabel("Bins:"))
        hist_controls.addWidget(self.hist_bins)
        hist_controls.addWidget(self.hist_log_scale)
        hist_controls.addStretch()

        layout.addLayout(hist_controls)

        return widget

    def _create_metadata_widget(self) -> QWidget:
        """Create the metadata widget for viewer tab."""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Metadata table
        self.metadata_table = QTableWidget()
        self.metadata_table.setColumnCount(2)
        self.metadata_table.setHorizontalHeaderLabels(["Property", "Value"])
        layout.addWidget(self.metadata_table)

        return widget

    def setup_batch_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the batch processing tab."""
        self.batch_widget = BatchProcessingWidget(self)  # Pass self (AreTomoGUI) as parent instead of tab_widget
        layout.addWidget(self.batch_widget)

    # Enhanced Analysis Tab Methods
    def _on_analysis_type_changed(self, analysis_type: str) -> None:
        """Handle analysis type change."""
        self.log_message(f"Analysis type changed to: {analysis_type}")
        # Update UI based on analysis type

    def _browse_analysis_files(self) -> None:
        """Browse for analysis files."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Analysis File", "",
            "All Files (*.*);;MRC Files (*.mrc);;Text Files (*.txt);;CSV Files (*.csv)"
        )
        if file_path:
            self.file_path_edit.setText(file_path)

    def _run_analysis(self) -> None:
        """Run the selected analysis."""
        analysis_type = self.analysis_type.currentText()
        file_path = self.file_path_edit.text()

        if not file_path:
            QMessageBox.warning(self, "Warning", "Please select a data file first.")
            return

        self.log_message(f"Running {analysis_type} on {file_path}")
        # Implement analysis logic here

    def _export_analysis(self) -> None:
        """Export analysis results."""
        self.log_message("Exporting analysis results...")
        # Implement export logic

    def _save_plot(self) -> None:
        """Save current plot."""
        self.log_message("Saving plot...")
        # Implement plot saving

    def _update_colormap(self, colormap: str) -> None:
        """Update visualization colormap."""
        self.log_message(f"Colormap changed to: {colormap}")

    def _update_plot_type(self, plot_type: str) -> None:
        """Update plot type."""
        self.log_message(f"Plot type changed to: {plot_type}")

    # Enhanced Viewer Tab Methods
    def _browse_viewer_directory(self) -> None:
        """Browse for viewer directory."""
        directory = QFileDialog.getExistingDirectory(
            self, "Select Directory", str(Path.home())
        )
        if directory:
            self.current_dir_edit.setText(directory)
            self._update_file_list()

    def _update_file_list(self) -> None:
        """Update the file list based on current directory and filter."""
        directory = self.current_dir_edit.text()
        if not directory or not os.path.exists(directory):
            return

        self.file_list.clear()
        filter_text = self.file_filter.currentText()

        # Simple file filtering based on extensions
        if "MRC" in filter_text:
            extensions = [".mrc"]
        elif "TIFF" in filter_text:
            extensions = [".tif", ".tiff"]
        elif "Image" in filter_text:
            extensions = [".png", ".jpg", ".jpeg"]
        elif "Data" in filter_text:
            extensions = [".txt", ".csv", ".dat"]
        else:
            extensions = None

        for file in os.listdir(directory):
            if extensions is None or any(file.lower().endswith(ext) for ext in extensions):
                self.file_list.addItem(file)

    def _on_file_selected(self, item) -> None:
        """Handle file selection."""
        file_name = item.text()
        directory = self.current_dir_edit.text()
        file_path = os.path.join(directory, file_name)
        self.viewer_status.setText(f"Selected: {file_name}")

    def _on_file_double_clicked(self, item) -> None:
        """Handle file double-click to load."""
        file_name = item.text()
        directory = self.current_dir_edit.text()
        file_path = os.path.join(directory, file_name)

        if file_name.lower().endswith('.mrc'):
            self.mrc_viewer.load_mrc(file_path)
            self.viewer_status.setText(f"Loaded: {file_name}")
            self._update_metadata(file_path)

    def _change_view_mode(self, mode: str) -> None:
        """Change viewer mode."""
        self.log_message(f"View mode changed to: {mode}")

    def _update_slice(self, value: int) -> None:
        """Update current slice."""
        self.slice_spinbox.setValue(value)
        # Update viewer

    def _update_slice_from_spinbox(self, value: int) -> None:
        """Update slice from spinbox."""
        self.slice_slider.setValue(value)

    def _update_contrast(self) -> None:
        """Update contrast settings."""
        min_val = self.contrast_min.value()
        max_val = self.contrast_max.value()
        self.log_message(f"Contrast updated: {min_val}-{max_val}")

    def _update_viewer_colormap(self, colormap: str) -> None:
        """Update viewer colormap."""
        self.log_message(f"Viewer colormap changed to: {colormap}")

    def _toggle_distance_tool(self, checked: bool) -> None:
        """Toggle distance measurement tool."""
        self.log_message(f"Distance tool: {'enabled' if checked else 'disabled'}")

    def _toggle_angle_tool(self, checked: bool) -> None:
        """Toggle angle measurement tool."""
        self.log_message(f"Angle tool: {'enabled' if checked else 'disabled'}")

    def _toggle_area_tool(self, checked: bool) -> None:
        """Toggle area measurement tool."""
        self.log_message(f"Area tool: {'enabled' if checked else 'disabled'}")

    def _show_line_profile(self) -> None:
        """Show line profile dialog."""
        self.log_message("Showing line profile...")

    def _save_current_view(self) -> None:
        """Save current view as image."""
        self.log_message("Saving current view...")

    def _save_movie(self) -> None:
        """Save movie of slices."""
        self.log_message("Saving movie...")

    def _export_viewer_data(self) -> None:
        """Export viewer data."""
        self.log_message("Exporting viewer data...")

    def _zoom_out(self) -> None:
        """Zoom out."""
        self.log_message("Zooming out...")

    def _zoom_fit(self) -> None:
        """Fit to window."""
        self.log_message("Fitting to window...")

    def _zoom_in(self) -> None:
        """Zoom in."""
        self.log_message("Zooming in...")

    def _change_orientation(self, orientation: str) -> None:
        """Change view orientation."""
        self.log_message(f"Orientation changed to: {orientation}")

    def _toggle_scale_bar(self, checked: bool) -> None:
        """Toggle scale bar."""
        self.log_message(f"Scale bar: {'shown' if checked else 'hidden'}")

    def _toggle_grid(self, checked: bool) -> None:
        """Toggle grid."""
        self.log_message(f"Grid: {'shown' if checked else 'hidden'}")

    def _update_histogram(self) -> None:
        """Update histogram display."""
        self.log_message("Updating histogram...")

    def _update_metadata(self, file_path: str) -> None:
        """Update metadata display."""
        try:
            # Clear existing metadata
            self.metadata_table.setRowCount(0)

            if file_path.lower().endswith('.mrc'):
                import mrcfile
                with mrcfile.open(file_path, permissive=True) as mrc:
                    # Add basic metadata
                    metadata = [
                        ("File Path", file_path),
                        ("Data Shape", str(mrc.data.shape)),
                        ("Data Type", str(mrc.data.dtype)),
                        ("Voxel Size", f"{mrc.voxel_size.x:.3f} x {mrc.voxel_size.y:.3f} x {mrc.voxel_size.z:.3f}"),
                        ("Min Value", f"{mrc.data.min():.3f}"),
                        ("Max Value", f"{mrc.data.max():.3f}"),
                        ("Mean Value", f"{mrc.data.mean():.3f}"),
                    ]

                    self.metadata_table.setRowCount(len(metadata))
                    for i, (key, value) in enumerate(metadata):
                        self.metadata_table.setItem(i, 0, QTableWidgetItem(key))
                        self.metadata_table.setItem(i, 1, QTableWidgetItem(str(value)))

        except Exception as e:
            self.log_message(f"Error loading metadata: {e}", "ERROR")

    def setup_export_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the export tab for various file format exports."""
        format_group = QGroupBox("Export Format", tab_widget)
        format_layout = QVBoxLayout()
        self.format_combo = QComboBox()
        self.format_combo.addItems([
            "IMOD MRC",
            "TIFF Stack",
            "Relion",
            "EMAN2",
            "ImageJ"
        ])
        format_layout.addWidget(self.format_combo)
        format_group.setLayout(format_layout)
        layout.addWidget(format_group)
        options_group = QGroupBox("Export Options", tab_widget)
        options_layout = QGridLayout()
        self.compression_check = QCheckBox("Use Compression")
        options_layout.addWidget(self.compression_check, 0, 0)
        self.calibration_check = QCheckBox("Include Calibration")
        options_layout.addWidget(self.calibration_check, 1, 0)
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        export_btn = QPushButton("Export")
        export_btn.clicked.connect(self.on_export)
        layout.addWidget(export_btn)
        layout.addStretch()

    def setup_log_tab(self, tab_widget: QWidget, layout: QVBoxLayout) -> None:
        """Set up the log tab for displaying application logs."""
        layout.addWidget(self.log_text)
        controls_layout = QHBoxLayout()
        clear_log_btn = QPushButton("Clear Log", tab_widget)
        clear_log_btn.clicked.connect(self.log_text.clear)
        save_log_btn = QPushButton("Save Log")
        save_log_btn.clicked.connect(self.on_save_log)
        controls_layout.addWidget(clear_log_btn)
        controls_layout.addWidget(save_log_btn)
        layout.addLayout(controls_layout)

    def on_browse_input(self) -> None:
        """Handle input directory browse button click."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Input Directory",
            str(Path.home()),
            QFileDialog.Option.ShowDirsOnly
        )
        if directory:
            self.input_dir.setText(directory)
            self._update_output_dir()  # Update output directory accordingly

    def _update_output_dir(self) -> None:
        """Update output directory based on input directory."""
        if self.input_dir.text():
            input_path = Path(self.input_dir.text())
            default_output = input_path.parent / f"{input_path.name}_output"
            self.output_dir.setText(str(default_output))

    def on_browse_output(self) -> None:
        """Handle output directory browse button click."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Output Directory",
            str(Path.home()),
            QFileDialog.ShowDirsOnly
        )
        if directory:
            self.output_dir.setText(directory)

    def on_browse_aretomo(self) -> None:
        """Handle AreTomo3 path browse button click."""
        # Set default path and filter based on OS
        if platform.system() == "Windows":
            default_path = "/usr/local/bin"
            file_filter = "All Files (*)"

        # Try environment variable first
                # Set a default path (user's home directory)
        default_path = os.path.expanduser('~')

        # Try environment variable first
        env_path = os.environ.get('ARETOMO3_PATH', '')
        if env_path and os.path.exists(env_path):
            if os.path.isdir(env_path):
                default_path = env_path
            else:
                default_path = os.path.dirname(env_path)

        # First try directory selection
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select AreTomo3 Directory",
            default_path,
            QFileDialog.Option.ShowDirsOnly
        )

        if directory:
            # First, try validating the directory itself (it might be the installation dir)
            success, message = validate_aretomo_installation(directory)
            if success:
                selected_path = os.path.join(directory, "AreTomo3")
            else:
                # If not a valid installation directory, let user select the executable
                executable, _ = QFileDialog.getOpenFileName(
                    self,
                    "Select AreTomo3 Executable",
                    directory,
                    file_filter
                )
                if not executable:
                    return
                success, message = validate_aretomo_installation(executable)
                if not success:
                    QMessageBox.warning(
                        self,
                        "Invalid AreTomo3 Executable",
                        f"Selected file is not a valid AreTomo3 executable:\n{message}\n\n"
                        "Please select the correct AreTomo3 executable file."
                    )
                    logger.warning(f"Invalid AreTomo3 executable selected: {executable} - {message}")
                    return
                selected_path = executable

            try:
                if not os.access(selected_path, os.X_OK):
                    os.chmod(selected_path, os.stat(selected_path).st_mode | 0o111)
            except Exception as e:
                QMessageBox.warning(
                    self,
                    "Permission Error",
                    f"Cannot make file executable: {str(e)}\nPlease ensure you have proper permissions."
                )
                return

            self.aretomo_path.setText(selected_path)
            # Update environment variable
            os.environ['ARETOMO3_PATH'] = selected_path
            logger.info(f"AreTomo3 path updated to: {selected_path}")
            self.status_bar.showMessage("AreTomo3 executable set successfully", 3000)

    def _show_about(self) -> None:
        """Show the about dialog."""
        version = "1.0.0"
        about_text = f"""
        <h2>AreTomo3 GUI</h2>
        <p>Version {version}</p>
        <p>A graphical interface for AreTomo3 tilt series alignment and reconstruction.</p>
        <p>Running with:</p>
        <ul>
        <li>Python {platform.python_version()}</li>
        <li>Qt {QT_VERSION_STR}</li>
        <li>OS: {platform.system()} {platform.release()}</li>
        </ul>
        <p>For help and documentation, please visit the project repository.</p>
        """
        QMessageBox.about(self, "About AreTomo3 GUI", about_text)
        """Show the about dialog."""
        version = "1.0.0"
        about_text = f"""
        <h2>AreTomo3 GUI</h2>
        <p>Version {version}</p>
        <p>A graphical interface for AreTomo3 tilt series alignment and reconstruction.</p>
        <p>Running with:</p>
        <ul>
        <li>Python {platform.python_version()}</li>
        <li>Qt {QT_VERSION_STR}</li>
        <li>OS: {platform.system()} {platform.release()}</li>
        </ul>
        <p>For help and documentation, please visit the project repository.</p>
        """

        QMessageBox.about(self, "About AreTomo3 GUI", about_text)

    def find_tilt_series(self, directory: str) -> Dict[str, TiltSeries]:
        """
        Find all tilt series in the specified directory.
        Uses TiltSeries.parse_tilt_series under the hood.

        Args:
            directory (str): Directory path to scan for tilt series

        Returns:
            Dict[str, TiltSeries]: Dictionary mapping position names to TiltSeries objects
        """
        logger.info(f"Scanning for tilt series in {directory}")
        try:
            series_dict = TiltSeries.parse_tilt_series(directory)
            if series_dict:
                logger.info(f"Found {len(series_dict)} tilt series")
            else:
                logger.info("No tilt series found")
            return series_dict
        except Exception as e:
            logger.error(f"Error finding tilt series: {str(e)}", exc_info=True)
            return {}

    def update_ui_from_series(self, series: TiltSeries) -> None:
        """Update UI controls with values from the loaded tilt series."""
        logger.info(f"Updating UI controls from series {series.position_name}")

        # Update microscope parameters
        p = series.series_params
        self.pixel_size.setValue(p['pixel_size'])
        self.tilt_axis.setValue(p['tilt_axis'])
        self.voltage.setValue(p['voltage'])
        self.cs.setValue(p['cs'])
        self.amp_contrast.setValue(p['amp_contrast'])
        self.frame_dose.setValue(p['frame_dose'])

        logger.info(f"Updated UI controls with parameters from .mdoc: {p}")

    def on_load_tilt_series(self) -> None:
        """Handle loading a tilt series from a file."""
        if not self.input_dir.text():
            QMessageBox.warning(
                self,
                "No Input Directory",
                "Please select an input directory first."
            )
            return

        input_path = Path(self.input_dir.text())
        if not input_path.exists():
            QMessageBox.warning(
                self,
                "Invalid Directory",
                f"Directory {input_path} does not exist."
            )
            return

        # Look for supported file types
        supported_types = ["*.mrc", "*.mrcs", "*.st", "*.tif", "*.tiff", "*.eer"]
        tilt_files = []
        for ext in supported_types:
            tilt_files.extend(input_path.glob(ext))

        if not tilt_files:
            QMessageBox.warning(
                self,
                "No Files Found",
                f"No supported tilt series files found in {input_path}.\n"
                "Supported formats: MRC, TIFF, EER"
            )
            return

        # Parse the tilt series and organize files
        self.tilt_series = TiltSeries.parse_tilt_series(str(input_path))

        # If we found any series, update the UI with values from the first one
        if self.tilt_series:
            first_series = next(iter(self.tilt_series.values()))
            self.update_ui_from_series(first_series)

        # Count files with and without mdoc
        files_with_mdoc = sum(1 for series in self.tilt_series.values() if series.mdoc_params is not None)
        total_series = len(self.tilt_series)

        self.status_bar.showMessage(
            f"Found {len(tilt_files)} files in {total_series} tilt series. "
            f"{files_with_mdoc}/{total_series} series have .mdoc files."
        )

    def get_selected_series(self) -> List[TiltSeries]:
        """Get list of selected tilt series from the UI."""
        if not self.tilt_series:
            return []

        # For now return all series, later we can add selection UI
        return list(self.tilt_series.values())

    def construct_aretomo_command(self, series: TiltSeries) -> str:
        """
        Construct AreTomo3 command for a tilt series with validation.

        Args:
            series: The tilt series to generate command for

        Returns:
            The complete AreTomo3 command string

        Raises:
            ValueError: If AreTomo3 path is not set or invalid
        """
        # Validate AreTomo3 directory
        aretomo_dir = self.aretomo_path.text()
        if not aretomo_dir:
            raise ValueError("AreTomo3 directory not set")

        success, message = validate_aretomo_installation(aretomo_dir)
        if not success:
            raise ValueError(f"Invalid AreTomo3 installation: {message}")

        # Preserve .mdoc values but allow UI overrides
        params = series.series_params.copy()

        # Update with UI values that are different from defaults
        ui_values = {
            'pixel_size': self.pixel_size.value(),
            'tilt_axis': self.tilt_axis.value(),
            'voltage': self.voltage.value(),
            'cs': self.cs.value(),
            'amp_contrast': self.amp_contrast.value(),
            'frame_dose': self.frame_dose.value()
        }

        # Update only if values are different from defaults
        for key, ui_value in ui_values.items():
            if abs(ui_value - series.series_params.get(key, 0)) > 1e-6:  # Use small epsilon for float comparison
                logger.debug(f"Updating {key} from UI: {series.series_params.get(key, 0)} -> {ui_value}")
                params[key] = ui_value

        # Update processing parameters from UI
        series.aretomo_params.update({
            'dark_tol': self.dark_tol.value(),
            'vol_z': self.volume_z.value(),
            'flip_gain': self.flip_gain.isChecked(),
            'flip_vol': self.flip_volume.isChecked(),
            'ctf': [self.correct_ctf.isChecked(), self.lowpass.value()]
        })

        # Build command using helper method
        return self._build_command(aretomo_dir, series)

    def _build_command(self, aretomo_dir: str, series: TiltSeries) -> str:
        """
        Build the actual AreTomo3 command string.

        Args:
            aretomo_dir: Path to AreTomo3 executable
            series: The tilt series to process

        Returns:
            The complete command string

        Raises:
            ValueError: If required parameters are missing
        """
        if not series.files:
            raise ValueError(f"No files found for series {series.position_name}")

        # Create output directory
        outdir = os.path.join(self.output_dir.text(), series.position_name)
        os.makedirs(outdir, exist_ok=True)

        # Basic command structure
        input_dir = os.path.dirname(series.files[0])

        # Start with empty command parts list
        cmd_parts = [aretomo_dir]  # Direct executable path

        # Add the input/output parameters first
        cmd_parts.extend([
            f'-InPrefix "{input_dir}/{series.position_name}"',
            '-InSuffix ".mdoc"',  # Using .mdoc as in the script
            f'-OutDir "{outdir}"'
        ])

        # Add microscope parameters
        p = series.series_params
        cmd_parts.extend([
            f"-PixSize {p['pixel_size']}",
            f"-Voltage {p['voltage']}",
            f"-TiltAxis {p['tilt_axis']}",
            f"-Cs {p['cs']}",
            f"-AmpContrast {p['amp_contrast']}",
        ])

        # Add motion correction parameters
        cmd_parts.extend([
            f"-McBin {self.mc_bin.value()}",
            f"-McPatch {self.mc_patch_x.value()} {self.mc_patch_y.value()}",
            f"-FmInt {self.fm_int.value()}",
            f"-FmDose {p['frame_dose']}",
            "-SplitSum 1"
        ])

        # Add tomogram generation parameters
        cmd_parts.extend([
            f"-VolZ {self.volume_z.value()}",
            f"-DarkTol {self.dark_tol.value()}",
            f"-AtBin {self.at_bin.value()}"
        ])

        # Add options based on checkboxes
        if self.flip_volume.isChecked():
            cmd_parts.append("-FlipVol")
        if self.out_xf.isChecked():
            cmd_parts.append("-OutXF")
        if self.out_imod.isChecked():
            cmd_parts.append("-OutImod")
        if self.wbp.isChecked():
            cmd_parts.append("-WBP 1")
        if self.tilt_cor.isChecked():
            cmd_parts.append("-TiltCor 1")
        if self.correct_ctf.isChecked():
            cmd_parts.extend(["-CtfMode 1", f"-CtfLowPass {self.lowpass.value()}"])

        # Add GPU selection if specified
        if self.gpu_index.value() > 0:
            cmd_parts.append(f"-Gpu {self.gpu_index.value()}")

        # Add gain reference if available
        if series.gain_ref:
            cmd_parts.append(f'-GainRef "{series.gain_ref}"')

        return " ".join(cmd_parts)

    def on_process(self) -> None:
        """Handle processing button click."""
        try:
            # Basic validation
            if not self.tilt_series:
                QMessageBox.warning(self, "No Data", "Please load tilt series data first.")
                return

            if not self.output_dir.text():
                QMessageBox.warning(self, "No Output", "Please select an output directory.")
                return

            if not self.aretomo_path.text():
                QMessageBox.warning(self, "No AreTomo3", "Please set the AreTomo3 directory path.")
                return

            # Create output directory
            try:
                os.makedirs(self.output_dir.text(), exist_ok=True)
            except OSError as e:
                QMessageBox.critical(
                    self,
                    "Output Directory Error",
                    f"Could not create output directory:\n{str(e)}"
                )
                return

            # Get selected series
            selected_series = self.get_selected_series()
            if not selected_series:
                QMessageBox.warning(
                    self,
                    "No Selection",
                    "No tilt series selected for processing."
                )
                return

            # Process each selected series
            for series in selected_series:
                try:
                    # Construct command
                    command = self.construct_aretomo_command(series)
                    logger.info(f"Starting processing for {series.position_name}")
                    logger.debug(f"Command: {command}")

                    # Update UI
                    self.log_text.append(f"Processing {series.position_name}...")
                    self.status_bar.showMessage(f"Processing {series.position_name}...")
                    self.progress_bar.setRange(0, 100)
                    self.progress_bar.setValue(0)

                    # Create and start worker
                    worker = AreTomoWorker(command)
                    worker.progress.connect(self.handle_progress)
                    worker.finished.connect(lambda s, m, ts=series: self.handle_process_finished(ts, s, m))
                    self.current_worker = worker
                    worker.start()

                except Exception as e:
                    logger.error(f"Error setting up processing for {series.position_name}: {str(e)}")
                    QMessageBox.warning(
                        self,
                        "Processing Setup Error",
                        f"Error setting up processing for {series.position_name}:\n{str(e)}"
                    )
                    continue

        except Exception as e:
            logger.error(f"Error during processing: {str(e)}", exc_info=True)
            QMessageBox.critical(
                self,
                "Processing Error",
                f"An error occurred during processing:\n{str(e)}"
            )

    def handle_process_finished(self, series: TiltSeries, success: bool, message: str) -> None:
        """Handle completion of a series processing."""
        logger.info(f"Processing finished for {series.position_name}: {message}")

        if success:
            self.status_bar.showMessage(f"Successfully processed {series.position_name}")
            self.log_text.append(f"✓ {series.position_name} processed successfully")

            # Update recent files list
            outfile = os.path.join(self.output_dir.text(), series.position_name, f"{series.position_name}.mrc")
            if os.path.exists(outfile):
                self.update_recent_files(outfile)

            # Add to viewer if it exists
            if hasattr(self, 'mrc_viewer') and self.mrc_viewer:
                self.mrc_viewer.load_mrc(outfile)

        else:
            self.status_bar.showMessage(f"Error processing {series.position_name}")
            self.log_text.append(f"✗ Error processing {series.position_name}: {message}")
            QMessageBox.warning(
                self,
                "Processing Error",
                f"Error processing {series.position_name}:\n{message}"
            )

    def handle_progress(self, message: str) -> None:
        """Handle progress updates from worker."""
        self.log_text.append(message)

        # Try to extract progress percentage
        try:
            if "progress:" in message.lower():
                progress = int(message.split(":")[-1].strip().rstrip("%"))
                self.progress_bar.setValue(progress)
        except ValueError:
            pass # Not a progress message

    def on_stop(self) -> None:
        """Handle stop button click."""
        # TODO: Implement proper stopping mechanism
        self.status_bar.showMessage("Stopping current operation...")

    def stop_batch_processing(self) -> None:
        """Stop current batch processing operations."""
        if self.current_worker and self.current_worker.isRunning():
            self.current_worker.terminate()
        self.is_processing = False
        self.processing_queue.clear()
        self.status_bar.showMessage("Batch processing stopped")

    def on_remove_from_queue(self) -> None:
        """Remove selected items from the processing queue."""
        selected_items = self.queue_list.selectedItems()
        for item in selected_items:
            self.queue_list.takeItem(self.queue_list.row(item))

    def on_clear_queue(self) -> None:
        """Clear all items from the processing queue."""
        self.queue_list.clear()

    def on_export(self) -> None:
        """Handle export button click."""
        if not self.output_dir.text():
            QMessageBox.warning(
                self,
                "No Output Directory",
                "Please select an output directory first."
            )
            return

        current_format = self.format_combo.currentText()
        options = {
            'compression': self.compression_check.isChecked(),
            'calibration': self.calibration_check.isChecked()
        }

        # TODO: Implement actual export functionality
        self.status_bar.showMessage(f"Exporting in {current_format} format...")

    def on_save_log(self) -> None:
        """Save current log to a file."""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Log File",
            str(Path.home() / "aretomo_log.txt"),
            "Text Files (*.txt)"
        )

        if file_path:
            try:
                with open(file_path, 'w') as f:
                    f.write(self.log_text.toPlainText())
                self.status_bar.showMessage(f"Log saved to {file_path}")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "Error Saving Log",
                    f"Could not save log: {str(e)}"
                )

    def show_debug_info(self) -> None:
        """Show debug info in the log tab."""
        # Get basic system info first
        debug_info = [
            "Application Initialization Info:",
            f"Qt Version: {QT_VERSION_STR}",
            f"Python Version: {platform.python_version()}",
            "System Information:",
            f"OS: {platform.system()} {platform.release()}",
            f"Machine: {platform.machine()}",
            f"CPU Count: {os.cpu_count()}",
            f"Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB",
            "",
        ]
        self.log_text.append("\n".join(debug_info))

        # Get GPU info separately to avoid blocking
        QTimer.singleShot(100, self._update_gpu_info)

    def _update_gpu_info(self) -> None:
        """Update GPU information in a non-blocking way."""
        try:
            gpu_info = GPUMonitor.get_gpu_info()
            if gpu_info and isinstance(gpu_info, dict):
                # Get GPU name or a default value
                gpu_name = gpu_info.get('name', 'Unknown GPU')
                # Get memory total or 0 if not available
                gpu_memory = gpu_info.get('memory_total', 0)
                gpu_text = f"GPU: {gpu_name} ({gpu_memory}MB)\n"
            else:
                gpu_text = "GPU: None detected\n"
            self.log_text.append(gpu_text)
        except Exception as e:
            self.log_text.append("GPU: Information unavailable\n")

    def handle_complete_series(self, series_info):
        """Called when a new tilt series is completed."""
        if series_info not in self.tilt_series:
            self.status_bar.showMessage(f"Completed tilt series: {series_info}")
            self.update_recent_files(series_info)

    def update_series_progress(self, series_info, progress):
        """Update progress for a tilt series."""
        self.status_bar.showMessage(f"{series_info}: {progress}%")

    def handle_new_files(self, file_list):
        """Called when new files are detected."""
        for file in file_list:
            if str(file).lower().endswith('.mrc'):
                self.recent_list.insertItem(0, str(file))

    def update_recent_files(self, file_path: str) -> None:
        """Update the recent files list."""
        # Add new item at the beginning
        self.recent_list.insertItem(0, str(file_path))

        # Keep only the last 10 items
        while self.recent_list.count() > 10:
            self.recent_list.takeItem(self.recent_list.count() - 1)

    def find_tilt_series(self, directory: str) -> Dict[str, TiltSeries]:
        """
        Find all tilt series in the specified directory.
        Uses TiltSeries.parse_tilt_series under the hood.

        Args:
            directory (str): Directory path to scan for tilt series

        Returns:
            Dict[str, TiltSeries]: Dictionary mapping position names to TiltSeries objects
        """
        logger.info(f"Scanning for tilt series in {directory}")
        try:
            series_dict = TiltSeries.parse_tilt_series(directory)
            if series_dict:
                logger.info(f"Found {len(series_dict)} tilt series")
            else:
                logger.info("No tilt series found")
            return series_dict
        except Exception as e:
            logger.error(f"Error finding tilt series: {str(e)}", exc_info=True)
            return {}

    def _connect_parameter_changes(self) -> None:
        """Connect parameter changes to update command preview."""
        # Microscope parameters
        self.pixel_size.valueChanged.connect(self._update_command_preview)
        self.tilt_axis.valueChanged.connect(self._update_command_preview)
        self.voltage.valueChanged.connect(self._update_command_preview)
        self.cs.valueChanged.connect(self._update_command_preview)
        self.amp_contrast.valueChanged.connect(self._update_command_preview)

        # Frame dose parameter
        self.frame_dose.valueChanged.connect(self._update_command_preview)

        # Processing parameters
        self.dark_tol.valueChanged.connect(self._update_command_preview)
        self.volume_z.valueChanged.connect(self._update_command_preview)
        self.lowpass.valueChanged.connect(self._update_command_preview)

        # Motion correction parameters
        self.mc_bin.valueChanged.connect(self._update_command_preview)
        self.mc_patch_x.valueChanged.connect(self._update_command_preview)
        self.mc_patch_y.valueChanged.connect(self._update_command_preview)
        self.fm_int.valueChanged.connect(self._update_command_preview)

        # Tomogram parameters
        self.at_bin.valueChanged.connect(self._update_command_preview)

        # Options
        self.flip_gain.stateChanged.connect(self._update_command_preview)
        self.flip_volume.stateChanged.connect(self._update_command_preview)
        self.correct_ctf.stateChanged.connect(self._update_command_preview)
        self.out_xf.stateChanged.connect(self._update_command_preview)
        self.out_imod.stateChanged.connect(self._update_command_preview)
        self.wbp.stateChanged.connect(self._update_command_preview)
        self.tilt_cor.stateChanged.connect(self._update_command_preview)

    def _update_command_preview(self) -> None:
        """Update the command preview when parameters change."""
        if not self.tilt_series:
            self.command_preview.setText("Load a tilt series to see the command preview")
            return

        try:
            # Get first series for preview
            series = next(iter(self.tilt_series.values()))
            command = self.construct_aretomo_command(series)
            self.command_preview.setText(command)
            # Log the update
            logger.debug(f"Updated command preview: {command}")
        except Exception as e:
            error_msg = f"Error updating command preview: {str(e)}"
            logger.error(error_msg)
            self.log_message(error_msg, level="ERROR")
            self.command_preview.setText("Error generating command preview")

    def _preview_command(self) -> None:
        """Generate and display the AreTomo3 command preview."""
        try:
            # Get currently selected series
            series_list = self.get_selected_series()
            if not series_list:
                self.command_preview.setText("No tilt series loaded")
                return

            # Use first series for preview
            first_series = series_list[0]
            command = self.construct_aretomo_command(first_series)
            self.command_preview.setText(command)

        except Exception as e:
            logger.error(f"Error generating command preview: {str(e)}", exc_info=True)
            self.command_preview.setText("Error generating command")

    def _browse_directory(self):
        """Browse for a directory."""
        path = QFileDialog.getExistingDirectory(
            self, "Select Directory",
            self.path_edit.text()
        )
        if path:
            self.path_edit.setText(path)
            self._update_file_list()

    def _update_file_list(self):
        """Update the file list based on current directory."""
        try:
            path = self.path_edit.text()
            if not os.path.exists(path):
                return

            self.file_list.clear()

            # Add all MRC and MDOC files
            files = []
            for pattern in ["*.mrc", "*.mdoc"]:
                files.extend(glob.glob(os.path.join(path, pattern)))

            for filepath in sorted(files):
                self.file_list.addItem(os.path.basename(filepath))

        except Exception as e:
            self.log_error(f"Error updating file list: {e}")

    def _filter_files(self):
        """Filter files based on search text."""
        filter_text = self.filter_edit.text().lower()

        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            item.setHidden(
                filter_text and filter_text not in item.text().lower()
            )

    def _update_preview_mode(self):
        """Update the preview display mode."""
        mode = self.view_mode.currentText()

        if mode == "Grid":
            self.preview_grid.setVisible(True)
        else:
            self.preview_grid.setVisible(False)

        # Update preview of selected files
        self._update_preview()

    def _update_preview(self):
        """Update the preview for selected files."""
        self.preview_grid.clear()

        selected_items = self.file_list.selectedItems()
        if not selected_items:
            return

        base_path = self.path_edit.text()

        for item in selected_items:
            filepath = os.path.join(base_path, item.text())
            if filepath.lower().endswith('.mrc'):
                try:
                    with mrcfile.open(filepath) as mrc:
                        # Get middle slice for preview
                        data = mrc.data
                        if data.ndim == 3:
                            slice_idx = data.shape[0] // 2
                            data = data[slice_idx]

                        # Create thumbnail
                        fig = Figure(figsize=(2, 2))
                        canvas = FigureCanvas(fig)
                        ax = fig.add_subplot(111)
                        ax.imshow(data, cmap='gray')
                        ax.axis('off')

                        # Convert to QPixmap
                        canvas.draw()
                        image = QImage(
                            canvas.buffer_rgba(),
                            canvas.width(),
                            canvas.height(),
                            QImage.Format.Format_RGBA8888
                        )
                        pixmap = QPixmap.fromImage(image)

                        # Get metadata
                        metadata = {
                            "Size": f"{data.shape[0]}x{data.shape[1]}",
                            "Type": str(data.dtype)
                        }

                        self.preview_grid.add_thumbnail(
                            filepath, pixmap, metadata
                        )

                except Exception as e:
                    self.log_error(f"Error creating preview for {filepath}: {e}")

    def _load_selected_file(self, filepath: str):
        """Load the selected file for processing."""
        if filepath.lower().endswith('.mrc'):
            try:
                # Switch to analysis tab
                self.tabs.setCurrentWidget(self.analysis_tab)

                # Load file in analysis viewer
                self.analysis_viewer.load_file(filepath)

            except Exception as e:
                self.log_error(f"Error loading file {filepath}: {e}")

    def _refresh_analysis(self):
        """Refresh the current analysis view."""
        if self.analysis_viewer:
            try:
                self.analysis_viewer.refresh()
            except Exception as e:
                self.log_error(f"Error refreshing analysis: {e}")

    def update_job_status(self, job_id: str, status: Dict[str, Any]):
        """Update the status of a running job."""
        found = False
        for row in range(self.job_list.rowCount()):
            if self.job_list.item(row, 0).text() == job_id:
                found = True
                # Update progress
                progress_bar = self.job_list.cellWidget(row, 1)
                progress_bar.setValue(int(status.get('progress', 0)))

                # Update resource usage
                resources = status.get('resources', {})
                resource_text = (
                    f"CPU: {resources.get('cpu', 0):.1f}% "
                    f"Mem: {resources.get('memory', 0):.1f}% "
                    f"GPU: {resources.get('gpu', 0):.1f}%"
                )
                self.job_list.setItem(row, 2, QTableWidgetItem(resource_text))

                # Update time
                elapsed = status.get('elapsed', 0)
                remaining = status.get('remaining', 0)
                time_text = (
                    f"Elapsed: {elapsed//60:02d}:{elapsed%60:02d} "
                    f"Remaining: {remaining//60:02d}:{remaining%60:02d}"
                )
                self.job_list.setItem(row, 3, QTableWidgetItem(time_text))

                # Update status
                self.job_list.setItem(
                    row, 4,
                    QTableWidgetItem(status.get('status', 'Unknown'))
                )
                break

        if not found and status.get('status') != 'completed':
            # Add new job row
            row = self.job_list.rowCount()
            self.job_list.insertRow(row)

            self.job_list.setItem(row, 0, QTableWidgetItem(job_id))

            progress = QProgressBar()
            progress.setRange(0, 100)
            progress.setValue(int(status.get('progress', 0)))
            self.job_list.setCellWidget(row, 1, progress)

            self.job_list.setItem(row, 2, QTableWidgetItem("Starting..."))
            self.job_list.setItem(row, 3, QTableWidgetItem("00:00 / --:--"))
            self.job_list.setItem(
                row, 4,
                QTableWidgetItem(status.get('status', 'Unknown'))
            )
