#!/usr/bin/env python3
"""
Test MRC viewer functionality with the real test tomogram.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, '/mnt/HDD/ak_devel/AT3Gui')

def test_mrc_file_access():
    """Test access to the test MRC file."""
    print("🧪 Testing MRC File Access...")
    
    test_mrc = Path("/mnt/HDD/ak_devel/AT3Gui/rec_TS_85.mrc")
    
    if not test_mrc.exists():
        print("   ❌ Test MRC file not found")
        return False
    
    print(f"   ✅ Test MRC file found: {test_mrc}")
    print(f"   ✅ File size: {test_mrc.stat().st_size / (1024*1024):.1f} MB")
    
    return True

def test_mrcfile_library():
    """Test mrcfile library functionality."""
    print("\n🧪 Testing mrcfile Library...")
    
    try:
        import mrcfile
        print("   ✅ mrcfile library imported")
        
        test_mrc = Path("/mnt/HDD/ak_devel/AT3Gui/rec_TS_85.mrc")
        if not test_mrc.exists():
            print("   ⚠️  Test MRC file not available for testing")
            return True
        
        # Test opening the file
        with mrcfile.open(str(test_mrc), permissive=True) as mrc:
            print(f"   ✅ MRC file opened successfully")
            print(f"   ✅ Data shape: {mrc.data.shape}")
            print(f"   ✅ Data type: {mrc.data.dtype}")
            
            if hasattr(mrc, 'voxel_size'):
                print(f"   ✅ Voxel size: {mrc.voxel_size}")
            
            # Test basic statistics
            data_min = mrc.data.min()
            data_max = mrc.data.max()
            data_mean = mrc.data.mean()
            print(f"   ✅ Data range: {data_min:.3f} to {data_max:.3f} (mean: {data_mean:.3f})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ mrcfile library test failed: {e}")
        return False

def test_mrc_viewer_import():
    """Test MRC viewer component import."""
    print("\n🧪 Testing MRC Viewer Import...")
    
    try:
        from aretomo3_gui.gui.viewers.mrc_viewer import MRCViewer
        print("   ✅ MRC viewer imported successfully")
        
        # Test viewer creation (without parent)
        viewer = MRCViewer()
        print("   ✅ MRC viewer created successfully")
        
        # Test basic methods exist
        if hasattr(viewer, 'load_mrc'):
            print("   ✅ load_mrc method available")
        
        if hasattr(viewer, 'update_display'):
            print("   ✅ update_display method available")
        
        return True
        
    except Exception as e:
        print(f"   ❌ MRC viewer import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_numpy_operations():
    """Test numpy operations on MRC data."""
    print("\n🧪 Testing NumPy Operations...")
    
    try:
        import numpy as np
        print("   ✅ NumPy imported")
        
        import mrcfile
        test_mrc = Path("/mnt/HDD/ak_devel/AT3Gui/rec_TS_85.mrc")
        
        if not test_mrc.exists():
            print("   ⚠️  Test MRC file not available")
            return True
        
        with mrcfile.open(str(test_mrc), permissive=True) as mrc:
            data = mrc.data
            
            # Test basic numpy operations
            print(f"   ✅ Data shape: {data.shape}")
            print(f"   ✅ Memory usage: {data.nbytes / (1024*1024):.1f} MB")
            
            # Test slicing (middle slice)
            if data.ndim == 3:
                middle_slice = data[data.shape[0] // 2]
                print(f"   ✅ Middle slice shape: {middle_slice.shape}")
                print(f"   ✅ Slice range: {middle_slice.min():.3f} to {middle_slice.max():.3f}")
            
            # Test histogram calculation
            hist, bins = np.histogram(data.flatten(), bins=50)
            print(f"   ✅ Histogram calculated: {len(hist)} bins")
        
        return True
        
    except Exception as e:
        print(f"   ❌ NumPy operations failed: {e}")
        return False

def test_matplotlib_integration():
    """Test matplotlib for visualization."""
    print("\n🧪 Testing Matplotlib Integration...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')  # Use non-interactive backend
        import matplotlib.pyplot as plt
        print("   ✅ Matplotlib imported with Agg backend")
        
        import numpy as np
        
        # Create a simple test plot
        fig, ax = plt.subplots(1, 1, figsize=(6, 4))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y)
        ax.set_title("Test Plot")
        
        # Test saving (don't actually save)
        print("   ✅ Matplotlib plotting works")
        plt.close(fig)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Matplotlib integration failed: {e}")
        return False

def test_pyqt6_widgets():
    """Test PyQt6 widget creation."""
    print("\n🧪 Testing PyQt6 Widgets...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QWidget, QLabel
        from PyQt6.QtCore import Qt
        
        # Create QApplication if it doesn't exist
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("   ✅ QApplication created")
        
        # Test widget creation
        widget = QWidget()
        label = QLabel("Test Label")
        print("   ✅ Basic widgets created")
        
        # Clean up
        widget.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"   ❌ PyQt6 widgets failed: {e}")
        return False

def main():
    """Run MRC viewer and data handling tests."""
    print("🔬 AT3GUI MRC Viewer & Data Handling Testing")
    print("=" * 60)
    
    test_results = []
    
    # Execute all test phases
    test_results.append(("MRC File Access", test_mrc_file_access()))
    test_results.append(("mrcfile Library", test_mrcfile_library()))
    test_results.append(("MRC Viewer Import", test_mrc_viewer_import()))
    test_results.append(("NumPy Operations", test_numpy_operations()))
    test_results.append(("Matplotlib Integration", test_matplotlib_integration()))
    test_results.append(("PyQt6 Widgets", test_pyqt6_widgets()))
    
    # Calculate results
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    success_rate = (passed / total) * 100
    
    print(f"\n{'='*60}")
    print("📊 MRC VIEWER & DATA HANDLING TEST RESULTS")
    print(f"{'='*60}")
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
    
    print(f"\n🎯 Overall Success Rate: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 95:
        print("🎉 EXCELLENT! MRC viewer functionality is perfect!")
        return 0
    elif success_rate >= 85:
        print("✅ GOOD! MRC viewer working well.")
        return 0
    elif success_rate >= 75:
        print("⚠️  ACCEPTABLE! Some MRC viewer issues to address.")
        return 1
    else:
        print("❌ CRITICAL! Major MRC viewer failures.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
