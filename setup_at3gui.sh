#!/bin/bash
# AT3GUI Setup Script
# Automated installation and setup for AreTomo3 GUI
# Compatible with Ubuntu, CentOS, and other Linux distributions

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

print_step() {
    echo -e "${CYAN}📋 $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to detect Linux distribution
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo "$ID"
    elif [ -f /etc/redhat-release ]; then
        echo "centos"
    elif [ -f /etc/debian_version ]; then
        echo "ubuntu"
    else
        echo "unknown"
    fi
}

# Function to install system dependencies
install_system_dependencies() {
    local distro=$(detect_distro)
    print_step "Installing system dependencies for $distro"

    case "$distro" in
        "ubuntu"|"debian")
            print_info "Updating package lists..."
            sudo apt-get update -qq

            print_info "Installing system dependencies..."
            sudo apt-get install -y \
                python3 \
                python3-pip \
                python3-venv \
                python3-dev \
                build-essential \
                pkg-config \
                libgl1-mesa-glx \
                libglib2.0-0 \
                libxkbcommon-x11-0 \
                libxcb-icccm4 \
                libxcb-image0 \
                libxcb-keysyms1 \
                libxcb-randr0 \
                libxcb-render-util0 \
                libxcb-xinerama0 \
                libxcb-xfixes0 \
                libfontconfig1 \
                libxrender1 \
                libxi6 \
                libxext6 \
                libx11-6 \
                git \
                wget \
                curl \
                xvfb
            ;;
        "centos"|"rhel"|"fedora")
            if command_exists dnf; then
                PKG_MANAGER="dnf"
            else
                PKG_MANAGER="yum"
            fi

            print_info "Installing system dependencies..."
            sudo $PKG_MANAGER install -y \
                python3 \
                python3-pip \
                python3-devel \
                gcc \
                gcc-c++ \
                make \
                pkgconfig \
                mesa-libGL \
                glib2 \
                libxkbcommon-x11 \
                xcb-util-icccm \
                xcb-util-image \
                xcb-util-keysyms \
                xcb-util-renderutil \
                xcb-util-wm \
                fontconfig \
                libXrender \
                libXi \
                libXext \
                libX11 \
                git \
                wget \
                curl \
                xorg-x11-server-Xvfb
            ;;
        *)
            print_warning "Unknown distribution: $distro"
            print_info "Please ensure the following are installed:"
            print_info "- Python 3.8+"
            print_info "- pip"
            print_info "- python3-venv"
            print_info "- Development tools (gcc, make)"
            print_info "- Qt/X11 libraries"
            ;;
    esac

    print_status "System dependencies installation completed"
}

# Main setup function
main() {
    print_header "AT3GUI Professional Installation Script"
    echo "======================================================="
    echo ""
    print_info "This script will install AT3GUI with all dependencies"
    print_info "Compatible with Ubuntu, CentOS, and other Linux distributions"
    echo ""

    # Check if running as root (not recommended)
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root is not recommended"
        print_info "AT3GUI will be installed in a virtual environment"
    fi

    # Install system dependencies if needed
    if [ "$INSTALL_SYSTEM_DEPS" = "true" ]; then
        print_step "Installing system dependencies..."
        install_system_dependencies
    else
        print_step "Checking system dependencies..."

        # Check Python installation
        if ! command_exists python3; then
            print_error "Python 3 is not installed."
            print_info "Run with --system-deps to install system dependencies automatically"
            print_info "Or install manually: sudo apt-get install python3 python3-pip python3-venv"
            exit 1
        fi

        # Check pip
        if ! command_exists pip3 && ! python3 -m pip --version >/dev/null 2>&1; then
            print_error "pip is not installed."
            print_info "Run with --system-deps to install system dependencies automatically"
            exit 1
        fi

        # Check venv
        if ! python3 -c "import venv" >/dev/null 2>&1; then
            print_error "python3-venv is not installed."
            print_info "Run with --system-deps to install system dependencies automatically"
            exit 1
        fi
    fi

    # Check Python version
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_info "Found Python $python_version"

    if ! python3 -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)"; then
        print_error "Python 3.8 or higher is required. Current version: $python_version"
        exit 1
    fi

    print_status "Python version is compatible"

    # Check if we're in the right directory
    if [ ! -f "aretomo3_gui/main.py" ]; then
        print_error "Please run this script from the AT3GUI root directory"
        print_info "Expected structure: AT3GUI/aretomo3_gui/main.py"
        exit 1
    fi

    print_status "AT3GUI directory structure verified"

    # Create virtual environment
    print_step "Setting up Python virtual environment..."
    if [ ! -d "venv" ]; then
        print_info "Creating virtual environment..."
        python3 -m venv venv --prompt "AT3GUI"
        print_status "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
        if [ "$CLEAN_INSTALL" = "true" ]; then
            print_info "Cleaning existing virtual environment..."
            rm -rf venv
            python3 -m venv venv --prompt "AT3GUI"
            print_status "Virtual environment recreated"
        fi
    fi

    # Activate virtual environment
    print_info "Activating virtual environment..."
    source venv/bin/activate

    # Verify virtual environment
    if [ "$VIRTUAL_ENV" != "$(pwd)/venv" ]; then
        print_error "Failed to activate virtual environment"
        exit 1
    fi
    print_status "Virtual environment activated: $VIRTUAL_ENV"

    # Upgrade pip and essential tools
    print_info "Upgrading pip and build tools..."
    pip install --upgrade pip setuptools wheel
    print_status "Build tools upgraded"

    # Install dependencies
    print_step "Installing AT3GUI dependencies..."
    if [ -f "requirements.txt" ]; then
        print_info "Installing production dependencies..."
        pip install -r requirements.txt
        print_status "Production dependencies installed successfully"

        # Install development dependencies if requested
        if [ "$DEV_INSTALL" = "true" ] && [ -f "requirements-dev.txt" ]; then
            print_info "Installing development dependencies..."
            pip install -r requirements-dev.txt
            print_status "Development dependencies installed successfully"
        fi
    else
        print_error "requirements.txt not found"
        exit 1
    fi

    # Verify critical dependencies
    print_step "Verifying critical dependencies..."
    python3 -c "
import sys
critical_packages = ['PyQt6', 'numpy', 'matplotlib', 'mrcfile', 'psutil']
failed = []

for pkg in critical_packages:
    try:
        __import__(pkg.lower())
        print(f'✅ {pkg} - OK')
    except ImportError:
        print(f'❌ {pkg} - FAILED')
        failed.append(pkg)

if failed:
    print(f'\\n❌ Critical packages failed: {failed}')
    sys.exit(1)
else:
    print('\\n✅ All critical dependencies verified')
"

    # Test installation
    print_step "Testing AT3GUI installation..."

    # Test basic import
    print_info "Testing basic import..."
    if python -c "import aretomo3_gui; print('✅ Basic import successful')" 2>/dev/null; then
        print_status "Basic import test passed"
    else
        print_error "Basic import test failed"
        exit 1
    fi

    # Test core functionality
    print_info "Testing core functionality..."
    if python -c "
from aretomo3_gui.core.logging_config import setup_logging
from aretomo3_gui.utils.export_functions import export_to_csv
print('✅ Core functionality test passed')
" 2>/dev/null; then
        print_status "Core functionality test passed"
    else
        print_warning "Core functionality test had issues (non-critical)"
    fi

    # Run comprehensive tests if available
    if [ -f "tests/validation/test_comprehensive_imports.py" ]; then
        print_info "Running comprehensive import tests..."
        if python tests/validation/test_comprehensive_imports.py >/dev/null 2>&1; then
            print_status "Comprehensive tests passed"
        else
            print_warning "Some comprehensive tests failed (non-critical for basic usage)"
        fi
    fi

    # Make launcher executable
    if [ -f "launch_at3gui.py" ]; then
        chmod +x launch_at3gui.py
        print_status "Launcher script made executable"
    fi

    # Make test runner executable
    if [ -f "run_tests.py" ]; then
        chmod +x run_tests.py
        print_status "Test runner made executable"
    fi

    # Create desktop launcher (Linux only)
    if command_exists xdg-user-dir && [ "$XDG_CURRENT_DESKTOP" ] && [ "$CREATE_DESKTOP_LAUNCHER" = "true" ]; then
        create_desktop_launcher
    fi

    # Success message
    echo ""
    print_header "🎉 AT3GUI Installation Complete!"
    echo "=============================================="
    echo ""
    print_status "Installation successful in virtual environment"
    print_info "Virtual environment: $(pwd)/venv"
    print_info "Python version: $python_version"
    print_info "Dependencies: $(pip list | wc -l) packages installed"
    echo ""

    print_header "🚀 How to Launch AT3GUI:"
    echo "1. Activate virtual environment:"
    echo "   source venv/bin/activate"
    echo ""
    echo "2. Launch AT3GUI (choose one):"
    echo "   python launch_at3gui.py          # Recommended"
    echo "   python -m aretomo3_gui.main      # Alternative"
    echo ""

    print_header "🧪 Testing AT3GUI:"
    echo "1. Run comprehensive tests:"
    echo "   python run_tests.py"
    echo ""
    echo "2. Test with sample data:"
    echo "   Load rec_TS_85.mrc (599.7 MB tomogram)"
    echo ""

    print_header "🔧 Troubleshooting:"
    echo "• For display issues (return code 132):"
    echo "  export QT_QPA_PLATFORM=offscreen"
    echo "• For SSH/remote access:"
    echo "  ssh -X username@hostname"
    echo "• For virtual display:"
    echo "  xvfb-run -a python launch_at3gui.py"
    echo ""

    print_status "AT3GUI is ready for use!"
    echo ""
}

# Function to create desktop launcher (Linux)
create_desktop_launcher() {
    local desktop_dir
    desktop_dir=$(xdg-user-dir DESKTOP 2>/dev/null || echo "$HOME/Desktop")
    local applications_dir="$HOME/.local/share/applications"

    if [ -d "$applications_dir" ]; then
        local at3gui_dir
        at3gui_dir=$(pwd)
        local launcher_file="$applications_dir/at3gui.desktop"

        cat > "$launcher_file" << EOF
[Desktop Entry]
Name=AT3GUI
Comment=AreTomo3 Graphical User Interface
Exec=$at3gui_dir/venv/bin/python $at3gui_dir/launch_at3gui.py
Path=$at3gui_dir
Icon=$at3gui_dir/icon.png
Terminal=false
Type=Application
Categories=Science;Education;Graphics;
StartupNotify=true
EOF

        chmod +x "$launcher_file"
        print_status "Desktop launcher created: $launcher_file"

        # Copy to desktop if it exists
        if [ -d "$desktop_dir" ]; then
            cp "$launcher_file" "$desktop_dir/"
            chmod +x "$desktop_dir/at3gui.desktop"
            print_status "Desktop shortcut created"
        fi
    fi
}

# Function to show usage
show_usage() {
    echo "AT3GUI Professional Installation Script"
    echo "======================================"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help           Show this help message"
    echo "  -d, --dev            Install development dependencies (pytest, etc.)"
    echo "  -c, --clean          Clean existing virtual environment"
    echo "  -s, --system-deps    Install system dependencies (requires sudo)"
    echo "  -l, --desktop        Create desktop launcher"
    echo "  -t, --test           Run tests after installation"
    echo "  -q, --quiet          Quiet installation (minimal output)"
    echo ""
    echo "Examples:"
    echo "  $0                   # Basic installation"
    echo "  $0 --system-deps     # Install system deps + AT3GUI"
    echo "  $0 --dev --test      # Development installation with testing"
    echo "  $0 --clean --dev     # Clean reinstall with dev dependencies"
    echo ""
    echo "For fresh Ubuntu/CentOS systems:"
    echo "  $0 --system-deps --desktop --test"
    echo ""
}

# Initialize variables
DEV_INSTALL=false
CLEAN_INSTALL=false
INSTALL_SYSTEM_DEPS=false
CREATE_DESKTOP_LAUNCHER=false
RUN_TESTS=false
QUIET_MODE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -d|--dev)
            DEV_INSTALL=true
            shift
            ;;
        -c|--clean)
            CLEAN_INSTALL=true
            shift
            ;;
        -s|--system-deps)
            INSTALL_SYSTEM_DEPS=true
            shift
            ;;
        -l|--desktop)
            CREATE_DESKTOP_LAUNCHER=true
            shift
            ;;
        -t|--test)
            RUN_TESTS=true
            shift
            ;;
        -q|--quiet)
            QUIET_MODE=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Clean installation if requested
if [ "$CLEAN_INSTALL" = true ]; then
    print_info "Cleaning existing installation..."
    rm -rf venv/
    print_status "Cleaned existing virtual environment"
fi

# Install development dependencies if requested
if [ "$DEV_INSTALL" = true ]; then
    print_info "Development installation mode enabled"
    # This will be handled in the main function
fi

# Run main setup
main

# Run tests if requested
if [ "$RUN_TESTS" = true ]; then
    print_header "🧪 Running AT3GUI Tests"
    echo "========================"
    echo ""

    # Ensure virtual environment is activated
    source venv/bin/activate

    if [ -f "run_tests.py" ]; then
        print_info "Running comprehensive test suite..."
        python run_tests.py
    elif [ -f "tests/validation/test_comprehensive_imports.py" ]; then
        print_info "Running import validation tests..."
        python tests/validation/test_comprehensive_imports.py
    else
        print_warning "No test files found"
    fi
fi

# Final summary
echo ""
print_header "🎉 AT3GUI Installation Summary"
echo "==============================="
echo ""
print_status "✅ Virtual environment: $(pwd)/venv"
print_status "✅ Python version: $(python3 --version)"
print_status "✅ Dependencies: Production packages installed"
if [ "$DEV_INSTALL" = true ]; then
    print_status "✅ Development dependencies: Installed"
fi
if [ "$INSTALL_SYSTEM_DEPS" = true ]; then
    print_status "✅ System dependencies: Installed"
fi
if [ "$CREATE_DESKTOP_LAUNCHER" = true ]; then
    print_status "✅ Desktop launcher: Created"
fi
echo ""

print_info "🚀 Ready to launch AT3GUI!"
print_info "Next steps:"
echo "  1. source venv/bin/activate"
echo "  2. python launch_at3gui.py"
echo ""

print_info "Setup script completed successfully!"
