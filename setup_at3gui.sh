#!/bin/bash
# AT3GUI Setup Script
# Automated installation and setup for AreTomo3 GUI

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Main setup function
main() {
    echo "🚀 AT3GUI Setup Script"
    echo "======================"
    echo ""
    
    # Check Python installation
    if ! command_exists python3; then
        print_error "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check Python version
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_info "Found Python $python_version"
    
    if ! python3 -c "import sys; sys.exit(0 if sys.version_info >= (3, 8) else 1)"; then
        print_error "Python 3.8 or higher is required. Current version: $python_version"
        exit 1
    fi
    
    print_status "Python version is compatible"
    
    # Check if we're in the right directory
    if [ ! -f "aretomo3_gui/main.py" ]; then
        print_error "Please run this script from the AT3GUI root directory"
        print_info "Expected structure: AT3GUI/aretomo3_gui/main.py"
        exit 1
    fi
    
    print_status "AT3GUI directory structure verified"
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        print_info "Creating virtual environment..."
        python3 -m venv venv
        print_status "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    print_info "Activating virtual environment..."
    source venv/bin/activate
    
    # Upgrade pip
    print_info "Upgrading pip..."
    pip install --upgrade pip setuptools wheel
    
    # Install dependencies
    print_info "Installing AT3GUI dependencies..."
    if [ -f "requirements.txt" ]; then
        pip install -r requirements.txt
        print_status "Dependencies installed successfully"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
    
    # Test installation
    print_info "Testing AT3GUI installation..."
    if python -c "import aretomo3_gui; print('✅ Import successful')" 2>/dev/null; then
        print_status "AT3GUI installation test passed"
    else
        print_error "AT3GUI installation test failed"
        exit 1
    fi
    
    # Make launcher executable
    if [ -f "launch_at3gui.py" ]; then
        chmod +x launch_at3gui.py
        print_status "Launcher script made executable"
    fi
    
    # Create desktop launcher (Linux only)
    if command_exists xdg-user-dir && [ "$XDG_CURRENT_DESKTOP" ]; then
        create_desktop_launcher
    fi
    
    # Success message
    echo ""
    echo "🎉 AT3GUI Setup Complete!"
    echo "========================"
    echo ""
    print_status "Installation successful"
    print_info "To launch AT3GUI:"
    echo "  1. Activate environment: source venv/bin/activate"
    echo "  2. Launch application: python launch_at3gui.py"
    echo "     OR: python -m aretomo3_gui.main"
    echo ""
    print_info "Test with sample data: rec_TS_85.mrc (599.7 MB tomogram)"
    echo ""
}

# Function to create desktop launcher (Linux)
create_desktop_launcher() {
    local desktop_dir
    desktop_dir=$(xdg-user-dir DESKTOP 2>/dev/null || echo "$HOME/Desktop")
    local applications_dir="$HOME/.local/share/applications"
    
    if [ -d "$applications_dir" ]; then
        local at3gui_dir
        at3gui_dir=$(pwd)
        local launcher_file="$applications_dir/at3gui.desktop"
        
        cat > "$launcher_file" << EOF
[Desktop Entry]
Name=AT3GUI
Comment=AreTomo3 Graphical User Interface
Exec=$at3gui_dir/venv/bin/python $at3gui_dir/launch_at3gui.py
Path=$at3gui_dir
Icon=$at3gui_dir/icon.png
Terminal=false
Type=Application
Categories=Science;Education;Graphics;
StartupNotify=true
EOF
        
        chmod +x "$launcher_file"
        print_status "Desktop launcher created: $launcher_file"
        
        # Copy to desktop if it exists
        if [ -d "$desktop_dir" ]; then
            cp "$launcher_file" "$desktop_dir/"
            chmod +x "$desktop_dir/at3gui.desktop"
            print_status "Desktop shortcut created"
        fi
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -d, --dev      Install development dependencies"
    echo "  -c, --clean    Clean existing installation"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -d|--dev)
            DEV_INSTALL=true
            shift
            ;;
        -c|--clean)
            CLEAN_INSTALL=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Clean installation if requested
if [ "$CLEAN_INSTALL" = true ]; then
    print_info "Cleaning existing installation..."
    rm -rf venv/
    print_status "Cleaned existing virtual environment"
fi

# Install development dependencies if requested
if [ "$DEV_INSTALL" = true ]; then
    print_info "Development installation mode enabled"
    # This will be handled in the main function
fi

# Run main setup
main

# Install development dependencies if requested
if [ "$DEV_INSTALL" = true ]; then
    print_info "Installing development dependencies..."
    source venv/bin/activate
    if [ -f "requirements-dev.txt" ]; then
        pip install -r requirements-dev.txt
        print_status "Development dependencies installed"
    else
        print_warning "requirements-dev.txt not found, skipping dev dependencies"
    fi
fi

print_info "Setup script completed successfully!"
